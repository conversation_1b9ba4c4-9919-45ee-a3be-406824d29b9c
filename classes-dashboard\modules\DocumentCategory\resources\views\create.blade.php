<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="card card-default">
                <div class="card-body">
                    {!! Form::open(['route' => 'documentCategory.store','id'=>'createdocumentCategory_form']) !!}
                    @include('DocumentCategory::fields')
                    {!! Form::close() !!}
                </div>
            </div>
        </div>
        <!-- /.card -->
    </div>
    </div>
</section>
{!! JsValidator::formRequest('DocumentCategory\Http\Requests\CreateDocumentCategoryRequest', '#createdocumentCategory_form') !!}
<script>
     var createdocumentCategoryRoute = {
        store: "{{ route('documentCategory.store') }}",
    };
</script>
<script src="{{ asset(mix('js/page-level-js/documentCategory/create.js')) }}"></script>