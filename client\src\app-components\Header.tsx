"use client";

import Link from "next/link";
import Image from "next/image";
import {
  Menu,
  X,
  User,
  ShoppingBag,
  Share2,
  UserCircle,
  LayoutDashboard,
  MessageSquare,
  Coins,
  ShoppingBagIcon,
  ShoppingCart,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useAppDispatch } from "@/store/hooks";
import { useEffect, useState, useRef } from "react";
import { isStudentAuthenticated, clearStudentAuthToken } from "@/lib/utils";
import ProfileCompletionIndicator from "./ProfileCompletionIndicator";
import NotificationBell from "./NotificationBell";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { toast } from "sonner";
import { logoutStudent } from "@/services/studentAuthServices";
import { clearUser } from "@/store/slices/userSlice";
import { clearStudentProfileData } from "@/store/slices/studentProfileSlice";
import { fetchStudentProfile } from "@/store/thunks/studentProfileThunks";
import { useRouter } from "next/navigation";
import { axiosInstance } from "@/lib/axios";
import { useMotionValue, useAnimationFrame } from "framer-motion";
import { generateJWT } from "@/services/AuthService";
import StreakDisplay from "@/components/ui/streakcountdisplay";

const Header = () => {
  const { isAuthenticated, user } = useSelector(
    (state: RootState) => state.user
  );
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);
  const [studentData, setStudentData] = useState<any>(null);
  const dispatch = useAppDispatch();
  const router = useRouter();

  const contentRef = useRef<HTMLDivElement>(null);
  const [contentWidth, setContentWidth] = useState(0);
  const x = useMotionValue(0);
  const speed = contentWidth / 20;

  useEffect(() => {
    const isLoggedIn = isStudentAuthenticated();
    setIsStudentLoggedIn(isLoggedIn);

    if (isLoggedIn) {
      const storedData = localStorage.getItem("student_data");
      if (storedData) {
        setStudentData(JSON.parse(storedData));
      }
      dispatch(fetchStudentProfile());
    }

    const handleStorageChange = () => {
      const newLoginStatus = isStudentAuthenticated();
      setIsStudentLoggedIn(newLoginStatus);
      if (newLoginStatus) {
        const storedData = localStorage.getItem("student_data");
        if (storedData) {
          setStudentData(JSON.parse(storedData));
        }
        dispatch(fetchStudentProfile());
      } else {
        setStudentData(null);
      }
    };

    window.addEventListener("storage", handleStorageChange);

    if (contentRef.current) {
      const width = contentRef.current.getBoundingClientRect().width;
      setContentWidth(width);
    }

    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, [dispatch]);

  useAnimationFrame((time, delta) => {
    if (contentWidth === 0) return;
    const currentX = x.get();
    const deltaX = (speed * delta) / 1000;
    let newX = currentX - deltaX;
    if (newX <= -contentWidth) {
      newX = 0;
    }
    x.set(newX);
  });

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const handleStudentLogout = async () => {
    try {
      const response = await logoutStudent();
      if (response.success !== false) {
        clearStudentAuthToken();
        setIsStudentLoggedIn(false);
        setStudentData(null);
        localStorage.removeItem("student_data");
        dispatch(clearStudentProfileData());
        toast.success("Logged out successfully");
        window.dispatchEvent(new Event("storage"));
      } else {
        toast.error(response.message || "Failed to logout");
        clearStudentAuthToken();
        setIsStudentLoggedIn(false);
        setStudentData(null);
        localStorage.removeItem("student_data");
        dispatch(clearStudentProfileData());
      }
    } catch (error) {
      console.log("Failed to logout", error);
      toast.error("Failed to logout");
      localStorage.removeItem("student_data");
      clearStudentAuthToken();
      setIsStudentLoggedIn(false);
      setStudentData(null);
      dispatch(clearStudentProfileData());
    }
  };

  const accessClassDashboard = async () => {
    try {
      const response = await generateJWT(user?.contactNo, user?.password);

      if (response.success) {
        const { token } = response.data;
        const redirectUrl = `${process.env.NEXT_PUBLIC_RANNDASS_URL}/login-class-link?uid=${user?.id}&token=${token}`;
        window.location.href = redirectUrl;
      } else {
        toast.error(response.message || "Failed to generate token");
      }
    } catch (error) {
      console.error("Failed to generate token", error);
      toast.error("Failed to generate token");
    }
  };

  const navLinks = [
    { href: "/verified-classes", label: "Find Tutor" },
    { href: "/uwhiz", label: "U - Whiz" },
    {
      href: "/mock-exam-card",
      label: (
        <span className="flex items-center gap-2">
          <span>Daily Quiz</span>
          {isStudentLoggedIn && <StreakDisplay studentId={studentData?.id} />}
        </span>
      ),
      isNew: true
    },
    { href: "/careers", label: "Career" },
    { href: "/store", label: "Store" },
  ];

  const classMenuItems = [
    {
      href: "/classes/profile",
      icon: <UserCircle className="w-5 h-5 mr-2" />,
      label: "Profile",
    },
    {
      href: "/classes/chat",
      icon: <MessageSquare className="w-5 h-5 mr-2" />,
      label: "Messages",
    },
    {
      href: "/coins",
      icon: <Coins className="w-5 h-5 mr-2" />,
      label: "Coins",
    },
    {
      href: "/classes/my-orders",
      icon: <ShoppingBag className="w-5 h-5 mr-2" />,
      label: "My Orders",
    },
    {
      onClick: accessClassDashboard,
      icon: <LayoutDashboard className="w-5 h-5 mr-2" />,
      label: "My Dashboard",
    },
    {
      href: "/classes/referral-dashboard",
      icon: <Share2 className="w-5 h-5 mr-2" />,
      label: "Referral Dashboard",
    },
  ];

  return (
    <>
      <header className="sticky top-0 z-50 w-full bg-black">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <Link
              href="/"
              className="flex items-center space-x-2 transition-transform hover:scale-105"
            >
              <Image
                src="/logo_black.png"
                alt="Preply Logo"
                width={120}
                height={40}
                className="rounded-sm"
              />
            </Link>

            <nav className="hidden md:flex items-center space-x-6">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className="relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400"
                >
                  {link.label}
                  {link.isNew && (
                    <span className="ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse">
                      Trending
                    </span>
                  )}
                </Link>
              ))}
            </nav>

            <div className="flex items-center space-x-3">
              {isAuthenticated || isStudentLoggedIn ? (
                <>
                  <Link href='' className="text-white"><ShoppingCart /></Link>
                  <NotificationBell
                    userType={isAuthenticated ? "class" : "student"}
                  />
                  <Popover>
                    <PopoverTrigger asChild>
                      <Avatar className="cursor-pointer h-9 w-9 hover:opacity-80 transition-opacity">
                        <AvatarFallback className="bg-white text-black flex items-center justify-center text-sm font-semibold">
                          {isAuthenticated
                            ? user?.firstName && user?.lastName
                              ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()
                              : "CT"
                            : studentData?.firstName && studentData?.lastName
                            ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()
                            : "ST"}
                        </AvatarFallback>
                      </Avatar>
                    </PopoverTrigger>
                    <PopoverContent className="w-64 bg-white p-4 rounded-lg shadow-lg">
                      <div className="flex items-center gap-3 mb-4">
                        <Avatar className="h-10 w-10">
                          <AvatarFallback className="bg-white text-black">
                            {isAuthenticated
                              ? user?.firstName && user?.lastName
                                ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()
                                : "CT"
                              : studentData?.firstName && studentData?.lastName
                              ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()
                              : "ST"}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium text-black">
                            {isAuthenticated
                              ? user?.firstName && user?.lastName
                                ? `${user.firstName} ${user.lastName}`
                                : user?.className || "Class Account"
                              : studentData?.firstName && studentData?.lastName
                              ? `${studentData.firstName} ${studentData.lastName}`
                              : "Student Account"}
                          </p>
                          <p className="text-xs text-gray-600">
                            {isAuthenticated
                              ? user?.contactNo || "<EMAIL>"
                              : studentData?.contactNo || "<EMAIL>"}
                          </p>
                        </div>
                      </div>

                      <div className="space-y-2">
                        {isAuthenticated ? (
                          <>
                            {classMenuItems.map((item) => (
                              <Button
                                asChild
                                variant="ghost"
                                className="w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground"
                                key={item.href || item.label}
                              >
                                {item.href ? (
                                  <Link
                                    href={item.href}
                                    className="flex items-center"
                                  >
                                    {item.icon}
                                    <span>{item.label}</span>
                                  </Link>
                                ) : (
                                  <div
                                    onClick={item.onClick}
                                    className="flex items-center w-full"
                                  >
                                    {item.icon}
                                    <span>{item.label}</span>
                                  </div>
                                )}
                              </Button>
                            ))}
                            <Button
                              variant="ghost"
                              className="w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md"
                              onClick={async () => {
                                try {
                                  const response = await axiosInstance.post(
                                    "/auth-client/logout",
                                    {}
                                  );
                                  if (response.data.success) {
                                    router.push("/");
                                    dispatch(clearUser());
                                    localStorage.removeItem("token");
                                    toast.success("Logged out successfully");
                                  }
                                } catch (error) {
                                  console.error("Logout error:", error);
                                  toast.error("Failed to logout");
                                }
                              }}
                            >
                              <User className="w-5 h-5 mr-2" />
                              <span>Logout</span>
                            </Button>
                          </>
                        ) : (
                          <>
                            <div className="space-y-2">
                              {[
                                {
                                  href: "/student/profile",
                                  icon: <UserCircle className="w-5 h-5 mr-2" />,
                                  label: "Profile",
                                },
                                {
                                  href: "/student/chat",
                                  icon: (
                                    <MessageSquare className="w-5 h-5 mr-2" />
                                  ),
                                  label: "Messages",
                                },
                                {
                                  href: "/coins",
                                  icon: <Coins className="w-5 h-5 mr-2" />,
                                  label: "Coins",
                                },
                                {
                                  href: "/student/wishlist",
                                  icon: (
                                    <ShoppingBag className="w-5 h-5 mr-2" />
                                  ),
                                  label: "My Wishlist",
                                },
                                {
                                  href: "/student/referral-dashboard",
                                  icon: <Share2 className="w-5 h-5 mr-2" />,
                                  label: "Referral Dashboard",
                                },
                                {
                                  href: "/student/my-orders",
                                  icon: (
                                    <ShoppingBag className="w-5 h-5 mr-2" />
                                  ),
                                  label: "My Orders",
                                },
                              ].map((item) => (
                                <Button
                                  asChild
                                  variant="ghost"
                                  className="w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground"
                                  key={item.href}
                                >
                                  <Link
                                    href={item.href}
                                    className="flex items-center"
                                  >
                                    {item.icon}
                                    <span>{item.label}</span>
                                  </Link>
                                </Button>
                              ))}
                              <Button
                                onClick={handleStudentLogout}
                                className="w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md"
                              >
                                <User className="w-5 h-5 mr-2" />
                                <span>Logout</span>
                              </Button>
                            </div>
                          </>
                        )}
                      </div>
                    </PopoverContent>
                  </Popover>
                </>
              ) : (
                <>
                  <div className="hidden md:flex items-center gap-2">
                    <Button
                      className="bg-[#ff914d] hover:bg-[#E88143] text-white text-sm px-4 py-2 rounded-md"
                      asChild
                    >
                      <Link href="/class/login">Join as Tutor</Link>
                    </Button>

                    <Button
                      variant="ghost"
                      className="bg-black text-white text-sm px-4 py-2 rounded-md border border-gray-700"
                      asChild
                    >
                      <Link href="/student/login">Student Login</Link>
                    </Button>
                  </div>
                </>
              )}

              <Button
                variant="ghost"
                size="icon"
                className="md:hidden text-orange-400 hover:bg-orange-500/10 rounded-full"
                onClick={toggleMenu}
              >
                {isMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div>
        <div
          className={`fixed inset-y-0 right-0 z-50 w-80 bg-black transform transition-all duration-300 ease-in-out md:hidden ${
            isMenuOpen ? "translate-x-0" : "translate-x-full"
          }`}
        >
          <div className="flex flex-col h-full p-6">
            <div className="flex items-center justify-between mb-6">
              <Image
                src="/logo_black.png"
                alt="Uest Logo"
                width={100}
                height={32}
                className="rounded-sm"
              />
              <Button
                variant="ghost"
                size="icon"
                className="text-orange-400 hover:bg-orange-500/10 rounded-full"
                onClick={toggleMenu}
              >
                <X className="h-6 w-6" />
              </Button>
            </div>

            {(isAuthenticated || isStudentLoggedIn) && (
              <div className="mb-6">
                <div className="flex items-center gap-3 p-3 bg-gray-900 rounded-lg">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback className="bg-white text-black">
                      {isAuthenticated
                        ? user?.firstName && user?.lastName
                          ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()
                          : "CT"
                        : studentData?.firstName && studentData?.lastName
                        ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()
                        : "ST"}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium text-white">
                      {isAuthenticated
                        ? user?.firstName && user?.lastName
                          ? `${user.firstName} ${user.lastName}`
                          : user?.className || "Class Account"
                        : studentData?.firstName && studentData?.lastName
                        ? `${studentData.firstName} ${studentData.lastName}`
                        : "Student Account"}
                    </p>
                    <p className="text-xs text-gray-400">
                      {isAuthenticated
                        ? user?.contactNo || "<EMAIL>"
                        : studentData?.contactNo || "<EMAIL>"}
                    </p>
                  </div>
                </div>
              </div>
            )}

            <nav className="flex flex-col space-y-2">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className="flex items-center justify-between gap-3 px-4 py-3 text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-gray-900 rounded-md border border-gray-700 transition-colors"
                  onClick={toggleMenu}
                >
                  <div className="flex items-center gap-3">
                    {typeof link.label === "string" ? (
                      <span>{link.label}</span>
                    ) : (
                      link.label
                    )}
                  </div>
                  {link.isNew && (
                    <span className="text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white">
                      Trending
                    </span>
                  )}
                </Link>
              ))}
            </nav>

            <div className="mt-auto space-y-2">
              {isAuthenticated && (
                <>
                  {classMenuItems.map((item) => (
                    <Button
                      asChild
                      variant="ghost"
                      className="w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground"
                      key={item.href || item.label}
                    >
                      {item.href ? (
                        <Link
                          href={item.href}
                          className="flex items-center"
                          onClick={toggleMenu}
                        >
                          {item.icon}
                          <span>{item.label}</span>
                        </Link>
                      ) : (
                        <div
                          onClick={() => {
                            toggleMenu();
                          }}
                          className="flex items-center w-full"
                        >
                          {item.icon}
                          <span>{item.label}</span>
                        </div>
                      )}
                    </Button>
                  ))}
                  <Button
                    variant="ghost"
                    className="w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md"
                    onClick={async () => {
                      try {
                        const response = await axiosInstance.post(
                          "/auth-client/logout",
                          {}
                        );
                        if (response.data.success) {
                          router.push("/");
                          dispatch(clearUser());
                          localStorage.removeItem("token");
                          toast.success("Logged out successfully");
                        }
                      } catch (error) {
                        console.error("Logout error:", error);
                        toast.error("Failed to logout");
                      }
                      toggleMenu();
                    }}
                  >
                    <User className="w-5 h-5 mr-2" />
                    <span>Logout</span>
                  </Button>
                </>
              )}

              {isStudentLoggedIn && (
                <>
                  {[
                    {
                      href: "/student/profile",
                      icon: <UserCircle className="w-5 h-5 mr-2" />,
                      label: "Profile",
                    },
                    {
                      href: "/student/chat",
                      icon: <MessageSquare className="w-5 h-5 mr-2" />,
                      label: "Messages",
                    },
                    {
                      href: "/coins",
                      icon: <Coins className="w-5 h-5 mr-2" />,
                      label: "Coins",
                    },
                    {
                      href: "/student/wishlist",
                      icon: <ShoppingBag className="w-5 h-5 mr-2" />,
                      label: "My Wishlist",
                    },
                    {
                      href: "/student/referral-dashboard",
                      icon: <Share2 className="w-5 h-5 mr-2" />,
                      label: "Referral Dashboard",
                    },
                    {
                      href: "/student/my-orders",
                      icon: <ShoppingBag className="w-5 h-5 mr-2" />,
                      label: "My Orders",
                    },
                  ].map((item) => (
                    <Button
                      asChild
                      variant="ghost"
                      className="w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground "
                      key={item.href}
                    >
                      <Link
                        href={item.href}
                        className="flex items-center"
                        onClick={toggleMenu}
                      >
                        {item.icon}
                        <span>{item.label}</span>
                      </Link>
                    </Button>
                  ))}
                  <Button
                    variant="ghost"
                    className="w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md"
                    onClick={() => {
                      handleStudentLogout();
                      toggleMenu();
                    }}
                  >
                    <User className="w-5 h-5 mr-2" />
                    <span>Logout</span>
                  </Button>
                </>
              )}

              {!isAuthenticated && !isStudentLoggedIn && (
                <div className="space-y-2">
                  <Button
                    className="w-full bg-[#ff914d] hover:bg-[#E88143] text-white rounded-lg py-3"
                    asChild
                  >
                    <Link href="/class/login" onClick={toggleMenu}>
                      Tutor Login
                    </Link>
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full text-[#ff914d] hover:bg-gray-900 rounded-lg py-3 border border-gray-700"
                    asChild
                  >
                    <Link href="/student/login" onClick={toggleMenu}>
                      Student Login
                    </Link>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>

        {isStudentLoggedIn && <ProfileCompletionIndicator />}
      </div>
    </>
  );
};

export default Header;
