import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const truncateThought = (text: string, wordLimit: number = 5): string => {
  const words = text.trim().split(/\s+/);
  if (words.length <= wordLimit) return text;
  return words.slice(0, wordLimit).join(' ') + '...';
};

export const setStudentAuthToken = (token: string) => {
  localStorage.setItem('studentToken', token);
};

export const getStudentAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('studentToken');
  }
  return null;
};

export const clearStudentAuthToken = () => {
  localStorage.removeItem('studentToken');
};

export const isStudentAuthenticated = (): boolean => {
  return !!getStudentAuthToken();
};

export const isAuthenticated = (): { isAuth: boolean; userType: 'STUDENT' | 'CLASS' | null } => {
  const studentToken = getStudentAuthToken();
  if (studentToken) {
    return { isAuth: true, userType: 'STUDENT' };
  }

  if (typeof window !== 'undefined') {
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        const user = JSON.parse(userData);
        if (user && user.id) {
          return { isAuth: true, userType: 'CLASS' };
        }
      } catch (error) {
        console.error('Error parsing user data:', error);
      }
    }
  }

  return { isAuth: false, userType: null };
};