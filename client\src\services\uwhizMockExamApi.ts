import { axiosInstance } from '@/lib/axios';

export const uwhizMockQuestionForStudent = async (studentId:string,medium:string): Promise<any[]> => {
  try {
    const response = await axiosInstance.get(
      `mock-exam/${studentId}/${medium}`,
      {
        headers: {
          "Server-Select": "uwhizServer",
        },
      }
    );
    return response.data;
  } catch (error: any) {
    throw new Error(
      `Failed To Get Question: ${error.response?.data?.message || error.message}`
    );
}
};