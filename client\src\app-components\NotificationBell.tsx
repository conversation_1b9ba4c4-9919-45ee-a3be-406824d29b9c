"use client";

import { useState, useEffect, useCallback } from 'react';
import { Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  getClassNotifications,
  getClassUnreadCount,
  markClassNotificationAsRead,
  markAllClassNotificationsAsRead,
  deleteAllClassNotifications,
  getStudentNotifications,
  getStudentUnreadCount,
  markStudentNotificationAsRead,
  markAllStudentNotificationsAsRead,
  deleteAllStudentNotifications,
  Notification
} from '@/services/notificationService';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';
import { useRouter } from 'next/navigation';

interface NotificationBellProps {
  userType: 'class' | 'student';
}

export default function NotificationBell({ userType }: NotificationBellProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const router = useRouter();

  const safeNotifications = Array.isArray(notifications) ? notifications : [];

  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true);
      let result: any;
      let count: number;

      if (userType === 'class') {
        result = await getClassNotifications(1, 20);
        count = await getClassUnreadCount();
      } else {
        result = await getStudentNotifications(1, 20);
        count = await getStudentUnreadCount();
      }

      // Handle both old and new response formats
      const notifs = result?.notifications || result || [];
      setNotifications(Array.isArray(notifs) ? notifs : []);
      setUnreadCount(count);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      setNotifications([]);
      setUnreadCount(0);
    } finally {
      setLoading(false);
    }
  }, [userType]);

  const handleNotificationClick = async (notification: Notification) => {
    try {
      // Mark notification as read
      if (userType === 'class') {
        await markClassNotificationAsRead(notification.id);
      } else {
        await markStudentNotificationAsRead(notification.id);
      }

      // Update local state
      setNotifications(prev =>
        prev.map(notif =>
          notif.id === notification.id ? { ...notif, isRead: true } : notif
        )
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
      setIsOpen(false);
      if (notification.data?.actionType === 'OPEN_CHAT' && notification.data?.redirectUrl) {
        router.push(notification.data.redirectUrl);
      }
    } catch (error) {
      console.error('Error handling notification click:', error);
      toast.error('Failed to process notification');
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      if (userType === 'class') {
        await markAllClassNotificationsAsRead();
      } else {
        await markAllStudentNotificationsAsRead();
      }

      // Update local state
      setNotifications(prev =>
        prev.map(notif => ({ ...notif, isRead: true }))
      );
      setUnreadCount(0);
      toast.success('All notifications marked as read');
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast.error('Failed to mark all notifications as read');
    }
  };

  const handleRemoveAllClick = () => {
    setShowDeleteDialog(true);
  };

  const handleConfirmRemoveAll = async () => {
    setShowDeleteDialog(false);

    try {
      if (userType === 'class') {
        await deleteAllClassNotifications();
      } else {
        await deleteAllStudentNotifications();
      }

      // Update local state
      setNotifications([]);
      setUnreadCount(0);
      toast.success('All notifications removed successfully');
    } catch (error) {
      console.error('Error removing all notifications:', error);
      toast.error('Failed to remove all notifications');
    }
  };

  useEffect(() => {
    fetchNotifications();

    const interval = setInterval(fetchNotifications, 30000);

    return () => clearInterval(interval);
  }, [fetchNotifications]);

  return (
    <>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="relative bg-black hover:bg-gray-900 transition duration-200 h-9 w-9 md:h-10 md:w-10 rounded-full"
          >
            <div className="absolute inset-0 rounded-full bg-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none" />

            <Bell className="relative z-10 h-4 w-4 md:h-5 md:w-5 text-white transition-colors duration-200" />

            {unreadCount > 0 && (
              <div className="absolute -top-1.5 -right-1.5 md:-top-2 md:-right-2 h-5 w-5 bg-red-500 rounded-full flex items-center justify-center z-20">
                <span className="text-white text-[10px] md:text-xs font-semibold leading-none">
                  {unreadCount > 99 ? '99+' : unreadCount}
                </span>
              </div>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="end">
          <div className="p-4 border-b">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold">Notifications</h3>
              <div className="flex gap-2">
                {unreadCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleMarkAllAsRead}
                    className="text-xs"
                  >
                    Mark all read
                  </Button>
                )}
                {notifications.length > 0 && unreadCount === 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleRemoveAllClick}
                    className="text-xs text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    Remove all
                  </Button>
                )}
              </div>
            </div>
          </div>
          <div className="h-80 overflow-y-auto">
            {loading ? (
              <div className="p-4 text-center text-muted-foreground">
                Loading notifications...
              </div>
            ) : notifications.length === 0 ? (
              <div className="p-4 text-center text-muted-foreground">
                No notifications yet
              </div>
            ) : (
              <div className="divide-y">
                {Array.isArray(notifications) && notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-4 cursor-pointer hover:bg-muted/50 transition-colors ${!notification.isRead ? 'bg-blue-50/50' : ''
                      }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex items-start gap-3">
                      <div className={`w-2 h-2 rounded-full mt-2 ${!notification.isRead ? 'bg-blue-500' : 'bg-gray-300'
                        }`} />
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm">{notification.title}</p>
                        <p className="text-sm text-muted-foreground mt-1">
                          {notification.message}
                        </p>
                        <p className="text-xs text-muted-foreground mt-2">
                          {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          {safeNotifications.length > 0 && (
            <div className="p-3 border-t bg-muted/30">
              <Button
                variant="ghost"
                size="sm"
                className="w-full text-xs"
                onClick={() => {
                  setIsOpen(false);
                  router.push('/notifications');
                }}
              >
                View All Notifications
              </Button>
            </div>
          )}
        </PopoverContent>
      </Popover>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove All Notifications</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove all notifications? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmRemoveAll}
              className="bg-red-600 hover:bg-red-700"
            >
              Remove All
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
