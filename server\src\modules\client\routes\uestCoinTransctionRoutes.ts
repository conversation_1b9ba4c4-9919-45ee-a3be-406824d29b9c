import { Router } from 'express';
import { addUestCoinTranscationController,updateUestCoinsController } from '../controllers/uestCoinTransctionController';
import { studentAuthMiddleware } from '@/middlewares/studentAuth';

const uestCoinTransactionRouter = Router();

uestCoinTransactionRouter.post('/add', studentAuthMiddleware, addUestCoinTranscationController);  
uestCoinTransactionRouter.post("/update", studentAuthMiddleware, updateUestCoinsController);

export default uestCoinTransactionRouter;
