import prisma from '@/config/prismaClient';

export const createOtp = async (contactNo: string) => {

  const otp = process.env.NODE_ENV !== "local" ? Math.floor(100000 + Math.random() * 900000).toString() : "445566";

  const expiredAt = new Date(Date.now() + 2 * 60 * 1000);

  // Check recent OTP requests
  const recentOtp = await prisma.otpMessage.findFirst({
    where: {
      contactNo,
    },
    orderBy: { createdAt: 'desc' },
  });

  // Reset requestCount if last request is older than 1 hour
  let requestCount = 1;
  if (recentOtp && recentOtp.createdAt > new Date(Date.now() - 60 * 60 * 1000)) {
    requestCount = recentOtp.requestCount + 1;
  }

  if (requestCount > 3) {
    throw new Error('Too many OTP requests, try again later');
  }

  await prisma.otpMessage.deleteMany({ where: { contactNo } });

  return prisma.otpMessage.create({
    data: {
      id: undefined,
      contactNo,
      otp,
      createdAt: new Date(),
      expiredAt,
      requestCount,
    },
  });
};

export const verifyOtp = async (contactNo: string, otp: string) => {
  const otpRecord = await prisma.otpMessage.findFirst({
    where: {
      contactNo,
      otp,
      expiredAt: { gt: new Date() },
    },
  });

  if (otpRecord) {
    await prisma.otpMessage.deleteMany({ where: { contactNo } });
  }

  return otpRecord;
};

export const findUserByEmail = async (email: string) => {
  return prisma.classes.findUnique({
    where: { email },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      contactNo: true,
      isVerified: true,
    },
  });
};

export const findUserByContactNo = async (contactNo: string) => {
  return prisma.classes.findFirst({
    where: { contactNo },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      contactNo: true,
      isVerified: true,
    },
  });
};

export const findUserByContactNoForLogin = async (contactNo: string) => {
  return prisma.classes.findFirst({
    where: { contactNo },
    select: {
      id: true,
      contactNo: true,
      firstName: true,
      lastName: true,
      isVerified: true,
    },
  });
};

export const createUser = async (
  firstName: string,
  lastName: string,
  contactNo: string
) => {
  const randomNumber = Math.floor(1000 + Math.random() * 9000);
  const username = `${firstName}${randomNumber}`;

  return prisma.classes.create({
    data: {
      firstName,
      lastName,
      contactNo,
      username,
      isVerified: true,
      createdAt: new Date(),
    },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      contactNo: true,
      username: true,
      isVerified: true,
    },
  });
};

export const updateUserContact = async (email: string, contactNo: string) => {
  return prisma.classes.update({
    where: { email },
    data: { contactNo, isVerified: true },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      contactNo: true,
      isVerified: true,
    },
  });
};