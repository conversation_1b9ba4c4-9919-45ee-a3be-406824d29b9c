import { useSelector } from 'react-redux';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { RootState } from '@/store';

export function useAuth() {
  const user = useSelector((state: RootState) => state.user.isAuthenticated);
  const router = useRouter();

  useEffect(() => {
    if (!user) {
      router.replace('/?authError=1');
    }
  }, [user, router]);

  return { user };
}
