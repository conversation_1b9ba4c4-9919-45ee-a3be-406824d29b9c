<?php

namespace DocumentCategory\Providers;

use Mo<PERSON>le\ModulesServiceProvider;

class DocumentCategoryServiceProvider extends ModulesServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        parent::register('DocumentCategory');
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot('DocumentCategory');
    }
}
