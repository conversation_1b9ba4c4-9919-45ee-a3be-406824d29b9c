'use client';

import React, { useEffect, useState, useRef, FormEvent, useMemo } from 'react';
import {
    Avatar,
    AvatarFallback,
} from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    ArrowLeft,
    Search,
    Send,
    X,
    MessageSquare,
    Users,
    Check,
    CheckCheck,
    RefreshCw
} from 'lucide-react';
import { io, Socket } from 'socket.io-client';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { ChatMessage, OnlineUser, SharedChatProps } from '@/lib/types';
import Image from 'next/image';
import { useIsMobile } from '@/hooks/use-mobile';
import { fetchingMessageUsers, fetchingprivateMessages } from '@/services/chatService';
import { useRouter } from 'next/navigation';
import EmojiPicker, { EmojiStyle } from 'emoji-picker-react';

export default function SharedChat({ userType, isAuthenticated, username, userId, initialSelectedUser, initialSelectedUserId, initialSelectedUserName }: SharedChatProps) {
    const [privateMessages, setPrivateMessages] = useState<ChatMessage[]>([]);
    const [messageInput, setMessageInput] = useState('');
    const [isUsernameSet, setIsUsernameSet] = useState(!!username);
    const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([]);
    const [offlineMessageUsers, setOfflineMessageUsers] = useState<Array<{ username: string; userId: string }>>([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedUser, setSelectedUser] = useState<string | null>(initialSelectedUser || null);
    const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
    const [currentRoomId, setCurrentRoomId] = useState<string | null>(null);
    const [sidebarOpen, setSidebarOpen] = useState(true);
    const [showEmojiPicker, setShowEmojiPicker] = useState(false);
    const [seenMessages, setSeenMessages] = useState<Set<string>>(new Set());
    const [recipientIsViewing, setRecipientIsViewing] = useState<boolean>(false);
    const [unreadMessageCounts, setUnreadMessageCounts] = useState<Map<string, number>>(new Map());
    const [userFilter, setUserFilter] = useState<'all' | 'unread'>('all');
    const router = useRouter();

    const messagesEndRef = useRef<HTMLDivElement>(null);
    const socketRef = useRef<Socket | null>(null);
    const emojiPickerRef = useRef<HTMLDivElement>(null);
    const isMobile = useIsMobile();

    const isProduction = process.env.NODE_ENV === "production";

    const socketUrl = isProduction
        ? process.env.NEXT_PUBLIC_SOCKET_URL_PROD
        : process.env.NEXT_PUBLIC_API_BASE_URL

    const socketPath = process.env.NEXT_PUBLIC_SOCKET_PATH || "/uapi/socket.io";

    const handleEmojiClick = (emojiData: any) => {
        setMessageInput((prev) => prev + emojiData.emoji);
    };

    const toggleEmojiPicker = (e: React.MouseEvent) => {
        e.preventDefault();
        setShowEmojiPicker((prev) => !prev);
    };

    useEffect(() => {
        setIsUsernameSet(!!username);
    }, [username]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (emojiPickerRef.current && !emojiPickerRef.current.contains(event.target as Node)) {
                setShowEmojiPicker(false);
            }
        };

        if (showEmojiPicker) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showEmojiPicker]);

    useEffect(() => {
        if (isAuthenticated && isUsernameSet && username) {
            if (socketRef.current) {
                socketRef.current.disconnect();
            }

            socketRef.current = io(socketUrl
                , {
                withCredentials: true,
                path: socketPath,
            });

            socketRef.current.on('connect', () => {
                socketRef.current?.emit('join', { username, userType, userId });
                socketRef.current?.emit('getOnlineUsers');
                socketRef.current?.emit('getUnreadCounts', { userId, userType });
            });

            socketRef.current.on('connect_error', (error) => {
                toast.error(`Connection error: ${error.message}`);
            });

            socketRef.current.on('roomJoined', (data: { roomId: string }) => {
                setCurrentRoomId(data.roomId);
            });

            socketRef.current.on('roomLeft', () => {
                setCurrentRoomId(null);
            });

            socketRef.current.on('messagesMarkedAsSeen', (data: { byUserId: string, messageIds: string[] }) => {
                if (data.byUserId === selectedUserId) {
                    setSeenMessages(prev => {
                        const newSet = new Set(prev);
                        data.messageIds.forEach(messageId => {
                            newSet.add(messageId);
                        });
                        return newSet;
                    });
                }
            });

            socketRef.current.on('privateMessage', (message: ChatMessage) => {
                if (selectedUser &&
                    ((message.sender === username && message.recipient === selectedUser) ||
                        (message.sender === selectedUser && message.recipient === username))) {
                    setPrivateMessages(prev => {
                        const messageExists = prev.some(msg => msg.id === message.id);
                        if (messageExists) {
                            return prev;
                        }
                        return [...prev, message];
                    });
                }

                if (message.sender !== username && !offlineMessageUsers.some(user => user.userId === message.senderId)) {
                    setOfflineMessageUsers(prev => [
                        ...prev,
                        { username: message.sender, userId: message.senderId }
                    ]);
                }
            });

            socketRef.current.on('onlineUsers', (users: OnlineUser[]) => {
                const uniqueUsers = Array.from(new Map(users.map(user => [user.userId, user])).values());
                setOnlineUsers(uniqueUsers);
            });

            socketRef.current.on('userStartedViewing', (data: { viewerId: string }) => {
                if (data.viewerId === selectedUserId) {
                    setRecipientIsViewing(true);
                }
            });

            socketRef.current.on('userStoppedViewing', (data: { viewerId: string }) => {
                if (data.viewerId === selectedUserId) {
                    setRecipientIsViewing(false);
                }
            });

            socketRef.current.on('unreadCountUpdate', (data: { senderId: string, senderName: string, unreadCount: number }) => {
                setUnreadMessageCounts(prev => {
                    const newMap = new Map(prev);
                    if (data.unreadCount === 0) {
                        newMap.delete(data.senderId);
                    } else {
                        newMap.set(data.senderId, data.unreadCount);
                    }
                    return newMap;
                });
            });

            socketRef.current.on('unreadCountsData', (data: Array<{ userId: string, unreadCount: number }>) => {
                const unreadCountsMap = new Map<string, number>();
                data.forEach((user: any) => {
                    unreadCountsMap.set(user.userId, user.unreadCount);
                });
                setUnreadMessageCounts(unreadCountsMap);
            });

            socketRef.current.on('updateMessageUsers', (data: { username: string, userId: string }) => {
                setOfflineMessageUsers(prev => {
                    if (!prev.some(user => user.userId === data.userId)) {
                        return [...prev, { username: data.username, userId: data.userId }];
                    }
                    return prev;
                });
            });

            socketRef.current.on('error', (error: { message: string }) => {
                toast.error(error.message);
            });

            return () => {
                if (currentRoomId && userId && selectedUserId && socketRef.current) {
                    socketRef.current.emit('leaveChatRoom', {
                        userId: userId,
                        recipientId: selectedUserId
                    });
                }

                socketRef.current?.off('connect');
                socketRef.current?.off('connect_error');
                socketRef.current?.off('privateMessage');
                socketRef.current?.off('onlineUsers');
                socketRef.current?.off('error');
                socketRef.current?.off('roomJoined');
                socketRef.current?.off('roomLeft');
                socketRef.current?.off('messagesMarkedAsSeen');
                socketRef.current?.off('userStartedViewing');
                socketRef.current?.off('userStoppedViewing');
                socketRef.current?.off('unreadCountUpdate');
                socketRef.current?.off('unreadCountsData');
                socketRef.current?.off('updateMessageUsers');
                socketRef.current?.disconnect();
            };
        }
    }, [username, isUsernameSet, isAuthenticated, userType, userId, selectedUser]);

    useEffect(() => {
        const fetchPrivateMessages = async () => {
            if (selectedUserId && userId && isUsernameSet) {
                try {
                    const data = await fetchingprivateMessages(userId, selectedUserId);
                    const uniqueMessages = (data || []).filter((message: any, index: number, self: any[]) =>
                        index === self.findIndex((m: any) => m.id === message.id)
                    );
                    setPrivateMessages(uniqueMessages);
                } catch {
                    toast.error('Failed to load messages. Please try again.');
                    setPrivateMessages([]);
                }
            }
        };

        fetchPrivateMessages();

        const refreshInterval = setInterval(() => {
            if (selectedUserId && userId && isUsernameSet) {
                fetchPrivateMessages();
            }
        }, 60000);

        return () => clearInterval(refreshInterval);
    }, [selectedUserId, userId, isUsernameSet]);

    useEffect(() => {
        const fetchMessageUsers = async () => {
            if (isUsernameSet && userId) {
                try {
                    const data = await fetchingMessageUsers(userId, userType);
                    const uniqueUsers = Array.from(new Map(data.map((user: any) => [user.userId, user])).values());
                    setOfflineMessageUsers(uniqueUsers as Array<{ username: string; userId: string }>);
                } catch {
                    // Silently handle error
                }
            }
        };

        fetchMessageUsers();

        const refreshInterval = setInterval(fetchMessageUsers, 30000);

        return () => clearInterval(refreshInterval);
    }, [isUsernameSet, userId, userType]);

    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [privateMessages]);

    useEffect(() => {
        if ((initialSelectedUser || initialSelectedUserId) && isMobile) {
            setSidebarOpen(false);
        } else {
            setSidebarOpen(!isMobile);
        }
    }, [initialSelectedUser, initialSelectedUserId, isMobile]);

    useEffect(() => {
        const handleBeforeUnload = () => {
            if (currentRoomId && userId && selectedUserId && socketRef.current) {
                socketRef.current.emit('leaveChatRoom', {
                    userId: userId,
                    recipientId: selectedUserId
                });
            }
        };

        window.addEventListener('beforeunload', handleBeforeUnload);

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, [currentRoomId, userId, selectedUserId]);

    useEffect(() => {
        if (selectedUserId && userId && privateMessages.length > 0) {
            const unseenMessages = privateMessages.filter(msg =>
                msg.sender === selectedUser &&
                msg.recipient === username &&
                !seenMessages.has(msg.id)
            );

            if (unseenMessages.length > 0) {
                const unseenMessageIds = unseenMessages.map(msg => msg.id);

                socketRef.current?.emit('markMessagesAsSeen', {
                    senderId: selectedUserId,
                    recipientId: userId,
                    messageIds: unseenMessageIds
                });
            }
        }
    }, [selectedUserId, userId, privateMessages, selectedUser, username, seenMessages]);

    useEffect(() => {
        if (selectedUserId && userId && socketRef.current) {
            socketRef.current.emit('joinChatRoom', {
                userId: userId,
                recipientId: selectedUserId
            });
        }

        return () => {
            if (currentRoomId && userId && selectedUserId && socketRef.current) {
                socketRef.current.emit('leaveChatRoom', {
                    userId: userId,
                    recipientId: selectedUserId
                });
            }
        };
    }, [selectedUserId, userId, currentRoomId]);

    const handleSendMessage = async (e: FormEvent) => {
        e.preventDefault();

        if (!messageInput.trim() || !selectedUser || !userId) {
            return;
        }

        const messageText = messageInput.trim();
        setMessageInput('');

        try {

            if (!selectedUserId) {
                toast.error('No recipient selected. Please select a user first.');
                setMessageInput(messageText);
                return;
            }

            const recipientType = userType === 'student' ? 'class' : 'student';

            const messageData = {
                text: messageText,
                senderId: userId,
                recipientId: selectedUserId,
                senderType: userType,
                recipientType: recipientType,
                recipientUsername: selectedUser
            };

            if(userType == 'student'){
                router.replace('/student/chat');
            }

            socketRef.current?.emit('sendPrivateMessage', messageData);

            if (!isUserOnline(selectedUser)) {
                toast.info(`${selectedUser} is offline. Your message will be delivered when they come online.`);
            }
        } catch {
            toast.error('Failed to send message. Please try again.');
            setMessageInput(messageText);
        }
    };

    const handleUserSelect = async (user: any) => {
        leaveCurrentRoom();

        setPrivateMessages([]);
        setSelectedUser(user.username);
        setSeenMessages(new Set());
        setRecipientIsViewing(false);

        const targetUserId = user.userId || user.username;
        setSelectedUserId(targetUserId);

        setUnreadMessageCounts(prev => {
            const newMap = new Map(prev);
            newMap.delete(targetUserId);
            return newMap;
        });

        if (isMobile) {
            setSidebarOpen(false);
        }

        if (targetUserId && userId) {
            try {
                const data = await fetchingprivateMessages(userId, targetUserId);
                setPrivateMessages(data || []);
            } catch {
                toast.error('Failed to load conversation history.');
            }
        }
    };

    const leaveCurrentRoom = () => {
        if (currentRoomId && userId && selectedUserId && socketRef.current) {
            socketRef.current.emit('leaveChatRoom', {
                userId: userId,
                recipientId: selectedUserId
            });
        }
    };

    const handleBackToSidebar = () => {
        leaveCurrentRoom();

        setSidebarOpen(true);
        if (isMobile) {
            setSelectedUser(null);
            setSelectedUserId(null);
        }
    };

    const formatTime = useMemo(() => {
        return (timestamp: Date) => format(new Date(timestamp), 'h:mm a');
    }, []);

    const allAvailableUsers = useMemo(() => {
        const userMap = new Map<string, { username: string; userType: string; userId: string }>();

        offlineMessageUsers.forEach(messageUser => {
            userMap.set(messageUser.userId, {
                username: messageUser.username,
                userType: userType === 'student' ? 'class' : 'student',
                userId: messageUser.userId
            });
        });

        onlineUsers.forEach(onlineUser => {
            if (onlineUser.username !== username && onlineUser.userType !== userType) {
                userMap.set(onlineUser.userId || onlineUser.username, {
                    username: onlineUser.username,
                    userType: onlineUser.userType,
                    userId: onlineUser.userId || onlineUser.username
                });
            }
        });

        if (initialSelectedUserId && initialSelectedUserName) {
            userMap.set(initialSelectedUserId, {
                username: initialSelectedUserName,
                userType: userType === 'student' ? 'class' : 'student',
                userId: initialSelectedUserId
            });
        }

        const filteredUsers = Array.from(userMap.values()).filter(user => {
            const matchesSearch = user.username.toLowerCase().includes(searchQuery.toLowerCase());
            const isNotCurrentUser = user.username !== username;
            const hasDifferentUserType = user.userType !== userType;

            return matchesSearch && isNotCurrentUser && hasDifferentUserType;
        });

        return filteredUsers;
    }, [offlineMessageUsers, onlineUsers, searchQuery, username, userType, initialSelectedUserId, initialSelectedUserName]);

    const isUserOnline = useMemo(() => {
        const onlineUserIds = new Set(onlineUsers.map(user => user.userId));
        return (username: string) => {
            const user = allAvailableUsers.find(u => u.username === username);
            if (user) {
                return onlineUserIds.has(user.userId);
            }

            if (username === initialSelectedUserName && initialSelectedUserId) {
                return onlineUserIds.has(initialSelectedUserId);
            }

            const onlineUser = onlineUsers.find(u => u.username === username);
            return !!onlineUser;
        };
    }, [onlineUsers, allAvailableUsers, initialSelectedUserName, initialSelectedUserId]);

    const unreadUsersCount = useMemo(() => {
        return allAvailableUsers.filter(user => {
            const userIdToCheck = user.userId;
            return unreadMessageCounts.has(userIdToCheck);
        }).length;
    }, [allAvailableUsers, unreadMessageCounts]);

    const filteredUsers = useMemo(() => {
        if (userFilter === 'unread') {
            return allAvailableUsers.filter(user => {
                const userIdToCheck = user.userId;
                return unreadMessageCounts.has(userIdToCheck);
            });
        }
        return allAvailableUsers;
    }, [allAvailableUsers, userFilter, unreadMessageCounts]);

    useEffect(() => {
        if (initialSelectedUserId && initialSelectedUserName && !selectedUserId) {
            setSelectedUser(initialSelectedUserName);
            setSelectedUserId(initialSelectedUserId);

            if (userId && isAuthenticated) {
                fetchingprivateMessages(userId, initialSelectedUserId)
                    .then(data => setPrivateMessages(data || []))
                    .catch(() => toast.error('Failed to load conversation history.'));
            }
        }
    }, [initialSelectedUserId, initialSelectedUserName, selectedUserId, userId, isAuthenticated]);

    useEffect(() => {
        if (initialSelectedUser && !selectedUserId && allAvailableUsers.length > 0 && !initialSelectedUserId) {
            const foundUser = allAvailableUsers.find(user => user.username === initialSelectedUser);
            if (foundUser) {
                setSelectedUser(foundUser.username);
                setSelectedUserId(foundUser.userId);

                if (foundUser.userId && userId) {
                    fetchingprivateMessages(userId, foundUser.userId)
                        .then(data => setPrivateMessages(data || []))
                        .catch(() => toast.error('Failed to load conversation history.'));
                }
            }
        }
    }, [initialSelectedUser, allAvailableUsers, selectedUserId, userId, initialSelectedUserId]);

    if (!isAuthenticated) {
        return (
            <div className="flex items-center justify-center min-h-[calc(100vh-64px)] bg-background p-4">
                <div className="w-full max-w-md p-4 sm:p-6 bg-card rounded-lg shadow-lg">
                    <h2 className="text-xl sm:text-2xl font-bold mb-4 sm:mb-6 text-center">Login Required</h2>
                    <div className="flex flex-col items-center justify-center text-center">
                        <p className="text-center text-muted-foreground mb-4 sm:mb-6 text-sm sm:text-base">
                            Please login as a student to access the chat feature.
                        </p>
                        <Button
                            onClick={() => router.push('/')}
                            className="w-full bg-orange-500 hover:bg-orange-600"
                        >
                            Go to Login
                        </Button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="flex h-[calc(100vh-64px)] bg-background text-foreground relative overflow-hidden">
            {sidebarOpen && (
                <aside className={`border-r border-gray-200 flex flex-col bg-gradient-to-b from-white to-gray-50 shadow-lg ${isMobile
                    ? 'absolute inset-0 z-50 w-full'
                    : 'relative w-80 min-w-80 lg:w-96 lg:min-w-96'
                    }`}>
                    <div className="p-4 flex items-center justify-between border-b border-gray-200 bg-white/80 backdrop-blur-sm">
                        <div className="flex items-center gap-3">
                            <div className="relative">
                                <Image
                                    src="/logo.png"
                                    alt="Uest Logo"
                                    width={isMobile ? 100 : 140}
                                    height={isMobile ? 25 : 35}
                                    className="object-contain cursor-pointer hover:opacity-80 transition-all duration-300 hover:scale-105"
                                    onClick={() => router.push('/')}
                                />
                            </div>
                        </div>
                        <div className="flex items-center gap-2">
                            <Button
                                variant="outline"
                                size={isMobile ? "sm" : "default"}
                                onClick={async () => {
                                    socketRef.current?.emit('getOnlineUsers');
                                    socketRef.current?.emit('getUnreadCounts', { userId, userType });

                                    if (isUsernameSet && userId) {
                                        try {
                                            const data = await fetchingMessageUsers(userId, userType);
                                            const uniqueUsers = Array.from(new Map(data.map((user: any) => [user.userId, user])).values());
                                            setOfflineMessageUsers(uniqueUsers as Array<{ username: string; userId: string }>);
                                        } catch {
                                            // Silently handle refresh error
                                        }
                                    }
                                }}
                                className={`bg-white/90 border border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 rounded-xl font-medium transition-all duration-300 shadow-sm hover:shadow-md ${isMobile ? 'px-3 py-2 text-xs' : 'px-4 py-2 text-sm'
                                    }`}
                                title="Refresh chat list"
                            >
                                <RefreshCw className={`${isMobile ? 'h-4 w-4' : 'h-4 w-4'}`} />
                                {!isMobile && <span className="ml-2">Refresh</span>}
                            </Button>
                            {isMobile && (
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    className="rounded-xl h-10 w-10 hover:bg-gray-100 transition-all duration-300"
                                    onClick={() => setSidebarOpen(false)}
                                >
                                    <X className="h-5 w-5" />
                                </Button>
                            )}
                        </div>
                    </div>

                    <div className={`${isMobile ? 'p-3' : 'p-4'} bg-white/50`}>
                        <div className="relative group">
                            <Search className={`absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 group-focus-within:text-gray-600 transition-colors duration-300 ${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />
                            <Input
                                placeholder="Search conversations..."
                                className={`pl-12 pr-4 bg-white border border-gray-300 rounded-xl text-gray-900 placeholder:text-gray-400 focus:border-black focus:ring-2 focus:ring-gray-100 transition-all duration-300 shadow-sm hover:shadow-md ${isMobile ? 'py-2.5 text-sm' : 'py-3 text-base'
                                    }`}
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />
                        </div>
                    </div>

                    <div className={`${isMobile ? 'px-3 pb-3' : 'px-4 pb-4'}`}>
                        <div className="bg-gray-100/80 rounded-2xl p-1.5 shadow-inner">
                            <div className="flex gap-1">
                                <button
                                    onClick={() => setUserFilter('all')}
                                    className={`relative flex-1 flex items-center justify-center gap-2 rounded-xl font-semibold transition-all duration-300 transform hover:scale-[1.02] ${
                                        userFilter === 'all'
                                            ? 'bg-white text-gray-900 shadow-lg border border-gray-200'
                                            : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                                    } ${isMobile ? 'px-3 py-2.5 text-xs' : 'px-4 py-3 text-sm'}`}
                                >
                                    <div className={`flex items-center gap-2 ${isMobile ? 'flex-col gap-1' : ''}`}>
                                        <Users className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />
                                        <span className={isMobile ? 'text-[10px] leading-tight' : ''}>
                                            {isMobile ? 'All' : 'All Users'}
                                        </span>
                                        <span className={`inline-flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-bold rounded-full transition-colors ${
                                            userFilter === 'all'
                                                ? 'bg-black text-white'
                                                : 'bg-gray-200 text-gray-600'
                                        }`}>
                                            {allAvailableUsers.length}
                                        </span>
                                    </div>
                                </button>

                                <button
                                    onClick={() => setUserFilter('unread')}
                                    className={`relative flex-1 flex items-center justify-center gap-2 rounded-xl font-semibold transition-all duration-300 transform hover:scale-[1.02] ${
                                        userFilter === 'unread'
                                            ? 'bg-white text-gray-900 shadow-lg border border-gray-200'
                                            : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                                    } ${isMobile ? 'px-3 py-2.5 text-xs' : 'px-4 py-3 text-sm'}`}
                                >
                                    <div className={`flex items-center gap-2 ${isMobile ? 'flex-col gap-1' : ''}`}>
                                        <div className="relative">
                                            <MessageSquare className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />
                                            {unreadUsersCount > 0 && (
                                                <div className="absolute -top-1 -right-1 bg-red-500 text-white text-[8px] rounded-full h-2 w-2 animate-pulse"></div>
                                            )}
                                        </div>
                                        <span className={isMobile ? 'text-[10px] leading-tight' : ''}>
                                            {isMobile ? 'Unread' : 'Unread Only'}
                                        </span>
                                        <span className={`inline-flex items-center justify-center min-w-[20px] h-5 px-1.5 text-xs font-bold rounded-full transition-colors ${
                                            userFilter === 'unread'
                                                ? unreadUsersCount > 0
                                                    ? 'bg-red-100 text-red-800'
                                                    : 'bg-gray-100 text-gray-600'
                                                : unreadUsersCount > 0
                                                    ? 'bg-red-500 text-white animate-pulse'
                                                    : 'bg-gray-200 text-gray-600'
                                        }`}>
                                            {unreadUsersCount}
                                        </span>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div className="flex-1 overflow-y-auto overscroll-contain">
                        <div className={`space-y-2 ${isMobile ? 'px-2 pb-2' : 'px-3 pb-3'}`}>
                            {filteredUsers.length > 0 || (selectedUser && initialSelectedUserId) ? (
                                <>
                                {selectedUser && initialSelectedUserId && initialSelectedUserName &&
                                 !filteredUsers.find(u => u.userId === initialSelectedUserId) && (
                                    <div
                                        key={initialSelectedUserId}
                                        className={`group transition-all duration-300 cursor-pointer rounded-2xl hover:shadow-lg hover:scale-[1.02] ${isMobile ? 'p-3' : 'p-4'
                                            } bg-gradient-to-r from-black to-gray-800 text-white shadow-xl border border-gray-700`}
                                        onClick={() => handleUserSelect({
                                            username: initialSelectedUserName,
                                            userType: userType === 'student' ? 'class' : 'student',
                                            userId: initialSelectedUserId
                                        })}
                                    >
                                        <div className="flex gap-3 items-center">
                                            <div className="relative">
                                                <Avatar className={`border-2 shadow-md transition-all duration-300 group-hover:scale-110 ${isMobile ? 'h-10 w-10' : 'h-12 w-12'} border-white/50`}>
                                                    <AvatarFallback className={`font-semibold transition-colors duration-300 ${isMobile ? 'text-xs' : 'text-sm'} bg-white text-black`}>
                                                        {initialSelectedUserName.substring(0, 2).toUpperCase()}
                                                    </AvatarFallback>
                                                </Avatar>
                                                <div className={`absolute -bottom-0.5 -right-0.5 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${isMobile ? 'h-4 w-4' : 'h-5 w-5'
                                                    } border-white ${isUserOnline(initialSelectedUserName) ? 'bg-green-500 shadow-lg' : 'bg-gray-400'
                                                    }`}>
                                                    <div className={`rounded-full transition-all duration-300 ${isMobile ? 'h-2 w-2' : 'h-2.5 w-2.5'
                                                        } ${isUserOnline(initialSelectedUserName) ? 'bg-green-300 animate-pulse' : 'bg-gray-300'
                                                        }`}></div>
                                                </div>
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <div className="flex justify-between items-center">
                                                    <h3 className={`font-semibold truncate transition-colors duration-300 ${isMobile ? 'text-sm' : 'text-base'} text-white`}>
                                                        {initialSelectedUserName}
                                                    </h3>
                                                </div>
                                                <div className="flex items-center gap-2 mt-1">
                                                    <div className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 bg-white/20 text-white`}>
                                                        Tutor
                                                    </div>
                                                    <div className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 bg-white/20 text-white`}>
                                                        <div className={`w-2 h-2 rounded-full ${isUserOnline(initialSelectedUserName) ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                                                        {isUserOnline(initialSelectedUserName) ? 'Online' : 'Offline'}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )}
                                {filteredUsers.map((user) => (
                                    <div
                                        key={user.userId} 
                                        className={`group transition-all duration-300 cursor-pointer rounded-2xl hover:shadow-lg hover:scale-[1.02] ${isMobile ? 'p-3' : 'p-4'
                                            } ${selectedUser === user.username
                                                ? 'bg-gradient-to-r from-black to-gray-800 text-white shadow-xl border border-gray-700'
                                                : 'bg-white border border-gray-200 hover:bg-gray-50 hover:border-gray-300'
                                            }`}
                                        onClick={() => handleUserSelect(user)}
                                    >
                                        <div className="flex gap-3 items-center">
                                            <div className="relative">
                                                <Avatar className={`border-2 shadow-md transition-all duration-300 group-hover:scale-110 ${isMobile ? 'h-10 w-10' : 'h-12 w-12'} ${
                                                    selectedUser === user.username
                                                        ? 'border-white/50'
                                                        : 'border-gray-300 group-hover:border-gray-400'
                                                }`}>
                                                    <AvatarFallback className={`font-semibold transition-colors duration-300 ${isMobile ? 'text-xs' : 'text-sm'} ${selectedUser === user.username
                                                        ? 'bg-white text-black'
                                                        : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-700 group-hover:from-gray-200 group-hover:to-gray-300'
                                                        }`}>
                                                        {user.username.substring(0, 2).toUpperCase()}
                                                    </AvatarFallback>
                                                </Avatar>
                                                <div className={`absolute -bottom-0.5 -right-0.5 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${isMobile ? 'h-4 w-4' : 'h-5 w-5'
                                                    } ${selectedUser === user.username ? 'border-white' : 'border-white'
                                                    } ${isUserOnline(user.username) ? 'bg-green-500 shadow-lg' : 'bg-gray-400'
                                                    }`}>
                                                    <div className={`rounded-full transition-all duration-300 ${isMobile ? 'h-2 w-2' : 'h-2.5 w-2.5'
                                                        } ${isUserOnline(user.username) ? 'bg-green-300 animate-pulse' : 'bg-gray-300'
                                                        }`}></div>
                                                </div>
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <div className="flex justify-between items-center">
                                                    <h3 className={`font-semibold truncate transition-colors duration-300 ${isMobile ? 'text-sm' : 'text-base'} ${selectedUser === user.username
                                                        ? 'text-white'
                                                        : 'text-gray-900 group-hover:text-black'
                                                        }`}>
                                                        {user.username}
                                                    </h3>
                                                    {unreadMessageCounts.has(user.userId) && (
                                                        <div className="bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold shadow-lg animate-pulse">
                                                            {unreadMessageCounts.get(user.userId)}
                                                        </div>
                                                    )}
                                                </div>
                                                <div className="flex items-center gap-2 mt-1">
                                                    <div className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 ${
                                                        selectedUser === user.username
                                                            ? 'bg-white/20 text-white'
                                                            : user.userType === 'student'
                                                                ? 'bg-gray-100 text-gray-700 group-hover:bg-gray-200'
                                                                : 'bg-gray-100 text-gray-700 group-hover:bg-gray-200'
                                                    }`}>
                                                        {user.userType === 'student' ? 'Student' : 'Tutor'}
                                                    </div>
                                                    <div className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium transition-colors duration-300 ${
                                                        selectedUser === user.username
                                                            ? 'bg-white/20 text-white'
                                                            : isUserOnline(user.username)
                                                                ? 'bg-green-100 text-green-700 group-hover:bg-green-200'
                                                                : 'bg-gray-100 text-gray-600 group-hover:bg-gray-200'
                                                    }`}>
                                                        <div className={`w-2 h-2 rounded-full ${isUserOnline(user.username) ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                                                        {isUserOnline(user.username) ? 'Online' : 'Offline'}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                                </>
                            ) : (
                                <div className="p-6 text-center">
                                    <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-sm">
                                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                            {userFilter === 'unread' ? (
                                                <MessageSquare className="h-8 w-8 text-gray-400" />
                                            ) : (
                                                <Users className="h-8 w-8 text-gray-400" />
                                            )}
                                        </div>
                                        <h3 className={`font-semibold text-gray-900 mb-2 ${isMobile ? 'text-sm' : 'text-base'}`}>
                                            {userFilter === 'unread' ? 'No unread messages' : 'No users found'}
                                        </h3>
                                        <p className="text-xs text-gray-600 mb-3">
                                            {userFilter === 'unread'
                                                ? 'All messages have been read or no conversations yet'
                                                : `You can only chat with ${userType === 'student' ? 'tutors' : 'students'} who have exchanged messages with you`
                                            }
                                        </p>
                                        <p className="text-xs text-gray-500">
                                            {userFilter === 'unread'
                                                ? 'Switch to "All Users" to see all conversations'
                                                : 'Users will appear here when you exchange messages with them'
                                            }
                                        </p>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </aside>
            )}

            {isMobile && sidebarOpen && (
                <div
                    className="absolute inset-0 bg-black/20 z-40"
                    onClick={() => setSidebarOpen(false)}
                />
            )}

            <main className="flex-1 flex flex-col min-w-0 bg-white">
                <div className={`border-b-2 border-gray-200 flex items-center gap-3 bg-white ${isMobile ? 'p-3' : 'p-4'}`}>
                    {isMobile && !sidebarOpen && (
                        <Button variant="ghost" size="icon" className={`flex-shrink-0 rounded-xl hover:bg-gray-100 ${isMobile ? 'h-8 w-8' : 'h-10 w-10'}`} onClick={handleBackToSidebar}>
                            <ArrowLeft className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />
                        </Button>
                    )}
                    <div className="flex gap-3 items-center min-w-0 flex-1">
                        <div className="relative">
                            <Avatar className={`border-2 border-gray-300 flex-shrink-0 shadow-md ${isMobile ? 'h-9 w-9' : 'h-12 w-12'}`}>
                                {selectedUser ? (
                                    <AvatarFallback className={`font-semibold bg-gray-100 text-black ${isMobile ? 'text-xs' : 'text-sm'}`}>
                                        {selectedUser.substring(0, 2).toUpperCase()}
                                    </AvatarFallback>
                                ) : (
                                    <AvatarFallback className="bg-gray-100">
                                        <Users className={`text-gray-500 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />
                                    </AvatarFallback>
                                )}
                            </Avatar>
                            {selectedUser && (
                                <div className={`absolute -bottom-1 -right-1 rounded-full border-2 border-white flex items-center justify-center ${isMobile ? 'h-3 w-3' : 'h-4 w-4'
                                    } ${isUserOnline(selectedUser) ? 'bg-green-500' : 'bg-gray-400'
                                    }`}>
                                    <div className={`rounded-full ${isMobile ? 'h-1.5 w-1.5' : 'h-2 w-2'
                                        } ${isUserOnline(selectedUser) ? 'bg-green-400 animate-pulse' : 'bg-gray-300'
                                        }`}></div>
                                </div>
                            )}
                        </div>
                        <div className="min-w-0 flex-1">
                            <h1 className={`font-semibold flex items-center gap-2 truncate text-black ${isMobile ? 'text-base' : 'text-lg'}`}>
                                {selectedUser ? (
                                    <span className="truncate">{selectedUser}</span>
                                ) : 'Select a user'}
                            </h1>
                            <p className={`text-gray-600 truncate ${isMobile ? 'text-xs' : 'text-sm'}`}>
                                {selectedUser ? (
                                    isUserOnline(selectedUser) ? 'Online' : 'Offline (messages will be delivered when online)'
                                ) : 'Choose someone to chat with'}
                            </p>
                        </div>
                    </div>
                </div>

                <div className={`flex-1 overflow-y-auto bg-gray-50 ${isMobile ? 'p-3' : 'p-4'}`}>
                    <div className={`mx-auto ${isMobile ? 'space-y-3 max-w-full' : 'space-y-4 max-w-4xl'}`}>
                        {selectedUser ? (
                            privateMessages.length > 0 ? (
                                privateMessages.map((message) => {
                                    const isCurrentUser = message.sender === username;
                                    const senderName = message.sender || 'Unknown';

                                    return (
                                        <div
                                            key={message.id}
                                            className={`flex items-end ${isCurrentUser ? 'justify-end' : ''} ${isMobile ? 'gap-2' : 'gap-3'}`}
                                        >
                                            {!isCurrentUser && (
                                                <Avatar className={`border-2 border-gray-300 shadow-sm ${isMobile ? 'h-6 w-6' : 'h-8 w-8'}`}>
                                                    <AvatarFallback className={`bg-gray-200 text-black font-semibold ${isMobile ? 'text-xs' : 'text-xs'}`}>
                                                        {senderName.substring(0, 2).toUpperCase()}
                                                    </AvatarFallback>
                                                </Avatar>
                                            )}
                                            <div className={`${isCurrentUser ? 'text-right' : ''} ${isMobile ? 'max-w-[80%]' : 'max-w-[70%]'}`}>
                                                <div
                                                    className={`${isCurrentUser
                                                        ? 'bg-black text-white'
                                                        : 'bg-white text-black border-2 border-gray-200'
                                                        } rounded-2xl shadow-lg break-words ${isMobile ? 'p-3' : 'p-4'}`}
                                                >
                                                    <div className={`leading-relaxed ${isMobile ? 'text-sm' : 'text-base'}`}>
                                                        {message.text}
                                                    </div>
                                                    <div className={`text-xs mt-2 flex items-end justify-end gap-1 ${isCurrentUser
                                                        ? 'text-gray-300'
                                                        : 'text-gray-500'
                                                        }`}>
                                                        {formatTime(message.timestamp)}
                                                        {isCurrentUser && (
                                                            <span>
                                                                {seenMessages.has(message.id) ? (
                                                                    <CheckCheck className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-blue-500`} />
                                                                ) : recipientIsViewing ? (
                                                                    <CheckCheck className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-blue-500`} />
                                                                ) : isUserOnline(selectedUser) ? (
                                                                    <CheckCheck className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-gray-400`} />
                                                                ) : (
                                                                    <Check className={`${isMobile ? 'h-3 w-3' : 'h-4 w-4'} text-gray-400`} />
                                                                )}
                                                            </span>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    );
                                })
                            ) : (
                                <div className="flex flex-col items-center justify-center h-full py-12 text-center">
                                    <MessageSquare className="text-gray-400 mb-4 h-16 w-16" />
                                    <p className="text-gray-600 text-lg font-medium">No messages yet</p>
                                    <p className="text-gray-500 text-sm mt-2">Send a message to start the conversation</p>
                                </div>
                            )
                        ) : (
                            <div className="flex flex-col items-center justify-center h-full py-12 text-center">
                                <MessageSquare className="text-gray-400 mb-4 h-16 w-16" />
                                <p className="text-gray-600 text-lg font-medium">Select a user to start chatting</p>
                                <p className="text-gray-500 text-sm mt-2">Choose a user from the sidebar to start a private conversation</p>
                                <p className="text-gray-500 text-sm mt-4 max-w-md">
                                    Note: You can only chat with {userType === 'student' ? 'tutors' : 'students'} who have exchanged messages with you.
                                    {filteredUsers.length === 0 && (
                                        <span className="block mt-2">There are currently no users to chat with. When you exchange messages with someone, they will appear in the sidebar.</span>
                                    )}
                                </p>
                            </div>
                        )}
                        <div ref={messagesEndRef} />
                    </div>
                </div>

                <form onSubmit={handleSendMessage} className={`border-t-2 border-gray-200 bg-white ${isMobile ? 'p-3' : 'p-4'}`}>
                    <div className={`flex items-center mx-auto ${isMobile ? 'gap-2 max-w-full' : 'gap-3 max-w-4xl'}`}>
                        <button
                            type="button"
                            onClick={toggleEmojiPicker}
                            className={`bg-white border-2 border-gray-200 text-black hover:bg-gray-100 rounded-xl font-medium transition-all duration-300 ${isMobile ? 'text-lg px-2 py-1' : 'text-2xl px-3 py-1'
                                }`}
                        >
                            😊
                        </button>

                        {showEmojiPicker && (
                            <div
                                ref={emojiPickerRef}
                                className={`absolute z-10 ${isMobile ? 'bottom-12 left-4 right-4' : 'bottom-12 left-96'
                                    }`}
                            >
                                <EmojiPicker
                                    onEmojiClick={handleEmojiClick}
                                    emojiStyle={EmojiStyle.APPLE}
                                    searchDisabled={true}
                                    width={isMobile ? '100%' : undefined}
                                />
                            </div>
                        )}
                        <Input
                            placeholder={selectedUser ? "Type your message..." : "Select a user to start chatting"}
                            className={`flex-1 bg-gray-50 border-2 border-gray-200 text-black placeholder:text-gray-500 focus:border-black rounded-xl transition-all duration-300 ${isMobile ? 'px-3 py-2 text-sm' : 'px-4 py-3 text-base'
                                }`}
                            value={messageInput}
                            onChange={(e) => setMessageInput(e.target.value)}
                            disabled={!selectedUser}
                            maxLength={250}
                        />
                        <Button
                            type="submit"
                            size={isMobile ? "default" : "lg"}
                            disabled={!messageInput.trim() || !selectedUser}
                            className={`bg-black text-white hover:bg-gray-800 rounded-xl font-medium transition-all duration-300 disabled:opacity-50 ${isMobile ? 'px-4 py-2' : 'px-6 py-3'
                                }`}
                        >
                            <Send className={`${isMobile ? 'h-4 w-4 mr-1' : 'h-5 w-5 mr-2'}`} />
                            {isMobile ? 'Send' : 'Send'}
                        </Button>
                    </div>
                </form>
            </main>
        </div>
    );
}