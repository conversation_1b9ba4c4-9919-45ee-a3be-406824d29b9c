var columns = [
    {
        data: "action",
        name: "action",
        orderable: false,
    },
    {
        data: "documentCategory_name",
        name: "documentCategory_name",
    },
    {
        data: "is_optional",
        name: "is_optional",
    },
    {
        data: "date",
        name: "date",
    },
];

var table = commonDatatable(
    "#documentCategory_table",
    documentCategoryRoute.index,
    columns
);

$(document).on("click", "#addDocumentCategoryEntry", function () {
    var params = $.extend({}, doAjax_params_default);
    params["url"] = documentCategoryRoute.create;
    params["requestType"] = `GET`;
    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        $("#modeltitle").html("Add New DocumentCategory");
        $("#createContent").html(result);
    };
    commonAjax(params);
});

$(document).on("click", ".deleteDocumentCategoryEntry", function () {
    var did = $(this).attr("data-deletedocumentCategoryid");
    var url = documentCategoryRoute.delete;
    url = url.replace(":did", did);

    var params = $.extend({}, doAjax_params_default);
    params["url"] = url;
    params["requestType"] = `DELETE`;
    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        toastr.success(result.success);
        table.draw();
    };
    var calert = function calert() {
        commonAjax(params);
    };
    commonAlert(calert);
});

$(document).ready(function () {
    function noSunday(date) {
        return [date.getDay() != 0, ""];
    }
    var year = new Date().getFullYear();
    $("body").delegate("#documentCategory_date", "focusin", function () {
        $(this).datepicker({
            dateFormat: "yy-mm-dd",
            changeMonth: true,
            changeYear: false,
            minDate: new Date(startYearDate),
            maxDate: new Date(endYearDate),
            beforeShowDay: noSunday,
        });
    });
});

$(document).on("click", ".editDocumentCategoryEntry", function () {
    var editid = $(this).attr("data-editdocumentCategoryid");
    var url = documentCategoryRoute.edit;
    url = url.replace(":editid", editid);

    var params = $.extend({}, doAjax_params_default);
    params["url"] = url;
    params["requestType"] = `GET`;
    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        $("#modeltitle").html("Edit Event");
        $("#createContent").html(result);
    };
    commonAjax(params);
});

$(document).on("click", ".exportData", function () {
    var url = documentCategoryRoute.export;
    var data = {};

    exportData(url, data);
});
