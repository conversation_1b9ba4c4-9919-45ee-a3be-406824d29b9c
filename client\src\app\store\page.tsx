"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { ShoppingBag, Star, Filter, Search, Coins, ShoppingCart, Plus, Minus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import { isAuthenticated } from "@/lib/utils";
import * as storeApi from "@/services/storeApi";
import * as storePurchaseApi from "@/services/storePurchaseApi";
import * as cartApi from "@/services/cartApi";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { StoreItem } from "@/lib/types";


const categories = ["All", "Stationery", "Toys", "Sports", "Other"];

const StorePage = () => {
  const router = useRouter();
  const [products, setProducts] = useState<StoreItem[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<StoreItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [sortBy, setSortBy] = useState("name");
  const [cart, setCart] = useState<cartApi.CartItem[]>([]);
  const [showCart, setShowCart] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [isUserLoggedIn, setIsUserLoggedIn] = useState(false);

  useEffect(() => {
    const authStatus = isAuthenticated();
    setIsUserLoggedIn(authStatus.isAuth);
    loadStoreItems();

    if (authStatus.isAuth) {
      loadCartItems();
    }
  }, []);

  const loadCartItems = async () => {
    try {
      const result = await cartApi.getCartItems();
      if (result.success && result.data) {
        setCart(result.data);
      }
    } catch (error) {
      console.error('Error loading cart:', error);
    }
  };

  const loadStoreItems = async () => {
    try {
      setLoading(true);
      const result = await storeApi.getAllStoreItems();

      if (!result.success) {
        throw new Error(result.error);
      }

      const items = result.data;
      setProducts(items);
    } catch (error: any) {
      console.error('Failed to load store items:', error);
      toast.error(error.message || 'Failed to load store items');
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    let filtered = products;

    if (selectedCategory !== "All") {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    switch (sortBy) {
      case "coin-price-low":
        filtered = [...filtered].sort((a, b) => a.coinPrice - b.coinPrice);
        break;
      case "coin-price-high":
        filtered = [...filtered].sort((a, b) => b.coinPrice - a.coinPrice);
        break;
      case "name":
        filtered = [...filtered].sort((a, b) => a.name.localeCompare(b.name));
        break;
      case "newest":
        filtered = [...filtered].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      default:
        filtered = [...filtered].sort((a, b) => a.name.localeCompare(b.name));
        break;
    }

    setFilteredProducts(filtered);
  }, [products, selectedCategory, searchQuery, sortBy]);

  const addToCart = async (product: StoreItem) => {
    if (!isUserLoggedIn) {
      toast.error("Please login to add items to cart");
      return;
    }

    if (product.availableStock === 0) {
      toast.error("Item is out of stock");
      return;
    }

    try {
      const result = await cartApi.addToCart(product.id, 1);
      if (result.success) {
        await loadCartItems(); 
        toast.success("Item added to cart!");
      } else {
        toast.error(result.error || "Failed to add item to cart");
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error("Failed to add item to cart");
    }
  };

  const removeFromCart = async (productId: string) => {
    try {
      const result = await cartApi.removeFromCart(productId);
      if (result.success) {
        await loadCartItems();
        toast.success("Item removed from cart!");
      } else {
        toast.error(result.error || "Failed to remove item from cart");
      }
    } catch (error) {
      console.error('Error removing from cart:', error);
      toast.error("Failed to remove item from cart");
    }
  };

  const updateQuantity = async (productId: string, newQuantity: number) => {
    try {

      const cartItem = cart.find(item => item.itemId === productId);

      if (cartItem && newQuantity > cartItem.item.availableStock) {
        toast.error(`Only ${cartItem.item.availableStock} items available in stock`);
        return;
      }

      const result = await cartApi.updateCartItemQuantity(productId, newQuantity);
      if (result.success) {
        await loadCartItems();
        if (newQuantity === 0) {
          toast.success("Item removed from cart!");
        }
      } else {
        toast.error(result.error || "Failed to update cart item");
      }
    } catch (error) {
      console.error('Error updating cart:', error);
      toast.error("Failed to update cart item");
    }
  };

  const getTotalPrice = () => {
    return cart.reduce((total, item) => {
      return total + (item.item.coinPrice * item.quantity);
    }, 0);
  };

  const handleCheckout = () => {
    if (cart.length === 0) {
      toast.error("Your cart is empty!");
      return;
    }

    if (!isUserLoggedIn) {
      toast.error("Please login to complete purchase");
      return;
    }

    setShowConfirmDialog(true);
  };

  const handleCardClick = (productId: string) => {
    router.push(`/store/${productId}`);
  };

  const confirmPurchase = async () => {
    try {
      const totalCoins = getTotalPrice();

      const purchaseData: storePurchaseApi.PurchaseData = {
        cartItems: cart.map(item => ({
          id: item.itemId,
          name: item.item.name,
          coinPrice: item.item.coinPrice,
          quantity: item.quantity,
          image: item.item.image || ''
        })),
        totalCoins
      };

      const result = await storePurchaseApi.purchaseItems(purchaseData);

      if (!result.success) {
        // Handle specific profile approval errors
        if (result.error === 'PROFILE_NOT_APPROVED') {
          // Use the detailed message from backend
          const errorMessage = result.data?.message || 'Your profile is not approved yet. Please complete your profile and wait for admin approval.';
          toast.error(errorMessage);
          setShowConfirmDialog(false);
          return;
        }
        throw new Error(result.error);
      }

      const orderId = result.data?.orderId || result.data?.firstOrderId || 'Unknown';
      toast.success(`Order placed successfully! Order ID: ${orderId.slice(-8)}. Coins deducted. Your order is pending admin approval.`);

      await loadCartItems();
      setShowCart(false);
      setShowConfirmDialog(false);

      loadStoreItems();
    } catch (error: any) {
      console.error('Error details:', error);
      toast.error(error.message || 'Purchase failed');
      setShowConfirmDialog(false);
    }
  };

  return (
    <>
      <Header />
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Hero Section */}
        <div className="relative bg-background dark:bg-gray-900 py-20 overflow-hidden border-b">
          <div className="container mx-auto px-4 text-center relative z-10">
            <div className="flex items-center justify-center gap-4 mb-6">
              <div className="p-3 bg-customOrange/10 rounded-xl">
                <ShoppingBag className="w-12 h-12 text-customOrange" />
              </div>
              <h1 className="text-5xl md:text-6xl font-bold text-foreground">
                UEST Store
              </h1>
            </div>
            <p className="text-xl md:text-2xl mb-8 text-muted-foreground max-w-3xl mx-auto">
              Premium educational products for students
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="flex items-center gap-2 px-4 py-2 bg-card rounded-full shadow-sm border">
                <Coins className="w-5 h-5 text-customOrange" />
                <span className="text-sm font-medium text-card-foreground">Pay with UEST Coins</span>
              </div>
              <div className="flex items-center gap-2 px-4 py-2 bg-card rounded-full shadow-sm border">
                <span className="w-2 h-2 bg-customOrange rounded-full"></span>
                <span className="text-sm font-medium text-card-foreground">Quality Products</span>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="container mx-auto px-4 py-8">
          <div className="bg-card rounded-xl shadow-sm border p-6 mb-8">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Filters */}
              <div className="flex flex-col sm:flex-row gap-4">
                {/* Category Filter */}
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-full sm:w-48">
                    <Filter className="w-4 h-4 mr-2 text-customOrange" />
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Sort */}
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Name (A-Z)</SelectItem>
                    <SelectItem value="coin-price-low">Coins: Low to High</SelectItem>
                    <SelectItem value="coin-price-high">Coins: High to Low</SelectItem>
                    <SelectItem value="newest">Newest First</SelectItem>
                  </SelectContent>
                </Select>

                {/* Cart Button */}
                <Button
                  onClick={() => setShowCart(true)}
                  className="bg-customOrange hover:bg-orange-600 relative"
                >
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Cart
                  {cart.length > 0 && (
                    <Badge className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground rounded-full w-5 h-5 flex items-center justify-center text-xs">
                      {cart.reduce((total, item) => total + item.quantity, 0)}
                    </Badge>
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* Payment Method Info */}
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mb-8 p-6 bg-card rounded-xl border">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-customOrange/10 rounded-lg">
                <Coins className="w-5 h-5 text-customOrange" />
              </div>
              <div>
                <h3 className="font-semibold text-card-foreground">Payment Method</h3>
                <p className="text-sm text-muted-foreground">All items are priced in UEST Coins</p>
              </div>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-orange-50 border border-orange-200 rounded-lg">
              <Coins className="w-4 h-4 text-orange-600" />
              <span className="font-medium text-orange-800">UEST Coins Only</span>
            </div>
          </div>

          {/* Products Grid */}
          {loading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {[...Array(8)].map((_, index) => (
                <Card key={index} className="overflow-hidden">
                  <Skeleton className="h-48 w-full" />
                  <CardContent className="p-4">
                    <Skeleton className="h-4 w-3/4 mb-2" />
                    <Skeleton className="h-3 w-full mb-2" />
                    <Skeleton className="h-3 w-2/3" />
                  </CardContent>
                  <CardFooter className="p-4">
                    <Skeleton className="h-10 w-full" />
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {filteredProducts.map((product, index) => (
                <Card
                  key={product.id}
                  className="overflow-hidden group bg-card border shadow-sm hover:shadow-lg transition-all duration-300 animate-fade-in-up cursor-pointer"
                  style={{ animationDelay: `${index * 0.1}s` }}
                  onClick={() => handleCardClick(product.id)}
                >
                <div className="relative h-64 bg-muted/30 flex items-center justify-center">
                    <Image
                      src={
                        product.image?.startsWith('http')
                          ? product.image
                          : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${product.image?.startsWith('/') ? product.image.substring(1) : product.image || 'uploads/store/placeholder.jpg'}`
                      }
                      alt={product.name}
                      className="object-contain w-full h-full transition-transform duration-300 group-hover:scale-105"
                      width={400}
                      height={256}
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = "/logo.png";
                      }}
                    />
                    {product.availableStock === 0 && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <Badge variant="destructive">Out of Stock</Badge>
                      </div>
                    )}
                  </div>

                  <CardContent className="p-4">
                    <h3 className="font-semibold text-lg mb-1 text-card-foreground line-clamp-1 group-hover:text-customOrange transition-colors">
                      {product.name}
                    </h3>
                    <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
                      {product.description}
                    </p>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-2xl font-bold text-customOrange flex items-center">
                          <Coins className="w-5 h-5 mr-1" />
                          {product.coinPrice} coins
                        </span>
                        <Badge variant="secondary" className="text-xs">
                          {product.category}
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground space-y-1">
                        <div>Available: <span className={`font-medium ${product.availableStock === 0 ? 'text-red-500' : 'text-green-600'}`}>{product.availableStock}</span></div>
                      </div>
                    </div>
                  </CardContent>

                  <CardFooter className="p-4 pt-0">
                    <Button
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent card click when button is clicked
                        addToCart(product);
                      }}
                      disabled={product.availableStock === 0}
                      className="w-full bg-customOrange hover:bg-orange-600 disabled:opacity-50"
                    >
                      {product.availableStock > 0 ? (
                        <>
                          <ShoppingCart className="w-4 h-4 mr-2" />
                          Add to Cart
                        </>
                      ) : (
                        'Out of Stock'
                      )}
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}

          {filteredProducts.length === 0 && !loading && (
            <div className="text-center py-16">
              <ShoppingBag className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-xl font-semibold text-card-foreground mb-2">
                No products found
              </h3>
              <p className="text-muted-foreground">
                Try adjusting your search or filter criteria
              </p>
            </div>
          )}
        </div>

        {/* Shopping Cart Dialog */}
        <Dialog open={showCart} onOpenChange={setShowCart}>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <ShoppingCart className="w-5 h-5" />
                Shopping Cart ({cart.reduce((total, item) => total + item.quantity, 0)} items)
              </DialogTitle>
              <DialogDescription>
                Review your items before checkout
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              {cart.length === 0 ? (
                <div className="text-center py-8">
                  <ShoppingCart className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">Your cart is empty</p>
                </div>
              ) : (
                <>
                  {cart.map((item) => (
                    <div key={item.id} className="flex items-center gap-4 p-4 border rounded-lg bg-card">
                      <Image
                        src={
                          item.item.image?.startsWith('http')
                            ? item.item.image
                            : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${item.item.image?.startsWith('/') ? item.item.image.substring(1) : item.item.image || 'uploads/store/placeholder.jpg'}`
                        }
                        alt={item.item.name}
                        width={60}
                        height={60}
                        className="rounded object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = "/placeholder-product.jpg";
                        }}
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-card-foreground">{item.item.name}</h4>
                        <p className="text-customOrange font-semibold flex items-center">
                          <Coins className="w-4 h-4 mr-1" />
                          {item.item.coinPrice} coins
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateQuantity(item.itemId, item.quantity - 1)}
                        >
                          <Minus className="w-3 h-3" />
                        </Button>
                        <span className="w-8 text-center">{item.quantity}</span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateQuantity(item.itemId, item.quantity + 1)}
                        >
                          <Plus className="w-3 h-3" />
                        </Button>
                      </div>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => removeFromCart(item.itemId)}
                      >
                        Remove
                      </Button>
                    </div>
                  ))}

                  <div className="border-t pt-4">
                    <div className="flex justify-between items-center text-lg font-semibold">
                      <span>Total:</span>
                      <span className="text-customOrange flex items-center">
                        <Coins className="w-5 h-5 mr-1" />
                        {getTotalPrice()} coins
                      </span>
                    </div>
                  </div>
                </>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setShowCart(false)}>
                Continue Shopping
              </Button>
              {cart.length > 0 && (
                <Button onClick={handleCheckout} className="bg-customOrange hover:bg-orange-600">
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Checkout
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Store Info Section */}
        <div className="bg-muted/30 py-16 mt-16">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div className="space-y-3">
                <div className="w-12 h-12 bg-customOrange/10 rounded-full flex items-center justify-center mx-auto">
                  <ShoppingBag className="w-6 h-6 text-customOrange" />
                </div>
                <h3 className="font-semibold text-lg text-card-foreground">Quality Products</h3>
                <p className="text-muted-foreground">Premium educational materials carefully selected for students</p>
              </div>
              <div className="space-y-3">
                <div className="w-12 h-12 bg-customOrange/10 rounded-full flex items-center justify-center mx-auto">
                  <Coins className="w-6 h-6 text-customOrange" />
                </div>
                <h3 className="font-semibold text-lg text-card-foreground">UEST Coins</h3>
                <p className="text-muted-foreground">Pay with your earned UEST coins for exclusive discounts</p>
              </div>
              <div className="space-y-3">
                <div className="w-12 h-12 bg-customOrange/10 rounded-full flex items-center justify-center mx-auto">
                  <Star className="w-6 h-6 text-customOrange" />
                </div>
                <h3 className="font-semibold text-lg text-card-foreground">Student Focused</h3>
                <p className="text-muted-foreground">Everything designed with student success in mind</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />

      {/* Purchase Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Purchase</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to purchase these items for <strong>{getTotalPrice()} coins</strong>?
              <br />
              <br />
              <div className="space-y-2">
                {cart.map((item) => (
                  <div key={item.id} className="flex justify-between text-sm">
                    <span>{item.item.name} x{item.quantity}</span>
                    <span>{item.item.coinPrice * item.quantity} coins</span>
                  </div>
                ))}
              </div>
              <br />
              This action cannot be undone and coins will be deducted from your account.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmPurchase}
              className="bg-customOrange hover:bg-customOrange/90"
            >
              Confirm Purchase
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default StorePage;