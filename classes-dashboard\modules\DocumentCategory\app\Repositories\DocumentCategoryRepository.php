<?php

namespace DocumentCategory\Repositories;

use DocumentCategory\Interfaces\DocumentCategoryInterface;
use Carbon\Carbon;
use DocumentCategory\Models\DocumentCategories;
use Illuminate\Support\Facades\Auth;

class DocumentCategoryRepository implements DocumentCategoryInterface
{
    protected $documentCategory;
    function __construct(DocumentCategories $documentCategory)
    {
        $this->documentCategory = $documentCategory;
    }

    public function getAll($request)
    {
        $documentCategory = $this->documentCategory->where('class_uuid', Auth::id());
    
        searchColumn($request->input('columns'), $documentCategory);
        orderColumn($request, $documentCategory, 'document_categories.id');

        return $documentCategory;
    }

    public function getDatatable($list)
    {
        return datatables()->of($list)
        ->addColumn('documentCategory_name', function ($data) {
            $name = $data->category_name;
            return $name;
        })
                ->addColumn('is_optional', function ($data) {
            return ($data->is_optional) ? "Yes" : "No";
        })
        ->addColumn('date', function ($data) {
            return date_formatter($data->created_at);
        })
        ->addColumn('action', function ($data) {
            $button = "";
            if (Auth::user()->can('update documentCategory')) {
                $button .= '<button data-toggle="modal" data-target="#newDocumentCategoryEntry" type="button" class="editDocumentCategoryEntry btn" title="Edit" data-editdocumentCategoryid="' . $data->id . '"><i class="fa fa-edit"></i></button>';
            }
            if (Auth::user()->can('delete documentCategory')) {
                $button .= '<button type="button" class="deleteDocumentCategoryEntry btn" title="Delete" data-deletedocumentCategoryid="' . $data->id . '"><i class="fa fa-trash"></i></button>';
            }
            return $button;
        })->rawColumns(['action'])
        ->make(true);
    }

    public function createDocumentCategory($data)
    {
        $this->documentCategory::create([
            'category_name' => $data->documentCategory_name,
            'class_uuid' => Auth::id(),
            'is_optional' => $data->is_optional ?? 0,
        ]);
    }

    public function getDocumentCategoryById($id)
    {
        return $this->documentCategory::Find($id);
    }

    public function getDocumentCategoryInArray()
    {
        $documentCategorys = $this->documentCategory::get();
        $disabledocumentCategory = [];
        foreach($documentCategorys as $documentCategory) {
            $disabledocumentCategory[] = Carbon::parse($documentCategory->date)->format('d-m-Y');
        }

        return $disabledocumentCategory;
    }

    public function updateDocumentCategory($data, $id)
    {
        $event = $this->getDocumentCategoryById($id);
        $event->category_name = $data->documentCategory_name;
        $event->is_optional = $data->is_optional;
        $event->save();
    }
}
