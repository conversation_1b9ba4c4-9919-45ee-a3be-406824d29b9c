<?php

namespace DocumentCategory\Http\Controllers;

use DocumentCategory\Http\Requests\CreateDocumentCategoryRequest;
use DocumentCategory\Repositories\DocumentCategoryRepository;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class DocumentCategoryController extends Controller
{

    protected $documentCategoryRepository;
    public function __construct(DocumentCategoryRepository $documentCategoryRepository)
    {
        $this->middleware('permission:read documentCategory', ['only' => ['index']]);
        $this->middleware('permission:create documentCategory', ['only' => ['create', 'store']]);
        $this->middleware('permission:update documentCategory', ['only' => ['edit', 'update']]);
        $this->middleware('permission:delete documentCategory', ['only' => ['destroy']]);
        $this->middleware('permission:export documentCategory data', ['only' => ['exportDocumentCategorys']]);
        $this->documentCategoryRepository = $documentCategoryRepository;
    }
    
    public function index(Request $request)
    {
        $list = $this->documentCategoryRepository->getAll($request);
        if (request()->ajax()) {
            return $this->documentCategoryRepository->getDatatable($list);
        }
        return view('DocumentCategory::index', compact('list'));
    }

    public function create()
    {
        return view('DocumentCategory::create');
    }

    public function store(CreateDocumentCategoryRequest $request)
    {
       $this->documentCategoryRepository->createDocumentCategory($request);
       return response()->json(['success' => 'DocumentCategory Created Successfully!!']);
    }

    public function edit($id)
    {
        $data = $this->documentCategoryRepository->getDocumentCategoryById($id);
        return view('DocumentCategory::edit', compact('data'));
    }

    public function update(CreateDocumentCategoryRequest $request, $id)
    {
        $this->documentCategoryRepository->updateDocumentCategory($request, $id);
        return response()->json(['success' => 'DocumentCategory Updated successfully!!']);
    }

    public function destroy($id)
    {
        $documentCategory = $this->documentCategoryRepository->getDocumentCategoryById($id);
        $documentCategory->delete();
        return response()->json(['success' => 'DocumentCategory deleted successfully!!']);
    }

    public function exportDocumentCategorys(Request $request) 
    {    
        $documentCategorys = $this->documentCategoryRepository->getAll($request)->get();
        return commonExport($documentCategorys, 'DocumentCategory::export', 'documentCategory');
    }
}
