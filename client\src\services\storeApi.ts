import { axiosInstance } from '@/lib/axios';
import { StoreFilters } from '@/lib/types';

export const getAllStoreItems = async (filters?: StoreFilters) => {
  try {
    const params = new URLSearchParams();
    if (filters?.category) params.append('category', filters.category);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.search) params.append('search', filters.search);

    params.append('status', 'ACTIVE');

    const response = await axiosInstance.get(`/admin/store?${params.toString()}`);
    return {
      success: true,
      data: response.data.data
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store items'
    };
  }
};

export const getStoreItemById = async (id: string) => {
  try {
    const response = await axiosInstance.get(`/admin/store/${id}`);
    return {
      success: true,
      data: response.data.data
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store item'
    };
  }
};

export const getStoreStats = async () => {
  try {
    const response = await axiosInstance.get('/admin/store/stats');
    return {
      success: true,
      data: response.data.data
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store statistics'
    };
  }
};
