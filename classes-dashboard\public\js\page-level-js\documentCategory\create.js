/******/ (() => { // webpackBootstrap
/*!***************************************************************!*\
  !*** ./modules/DocumentCategory/resources/views/js/create.js ***!
  \***************************************************************/
$("#createdocumentCategory_form").submit(function () {
  event.preventDefault();
  var form = $(this);
  if ($(this).valid()) {
    ajaxHandler(form, createdocumentCategoryRoute.store, 'post', '#createdocumentCategory_form', '#savedocumentCategory', '#newDocumentCategoryEntry', '#documentCategory_table');
    return false;
  }
});
/******/ })()
;