generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Classes {
  id              String                @id @default(uuid())
  firstName       String
  lastName        String
  className       String?
  email           String?               @unique
  createdAt       DateTime              @default(now())
  contactNo       String?
  username        String                @unique
  isVerified      Boolean               @default(false)
  blogs           Blog[]
  ClassAbout      ClassesAbout?
  address         ClassesAddress?
  certificates    ClassesCertificates[]
  education       ClassesEducation[]
  experience      ClassesExpereince[]
  classesReviews  ClassesReviews[]
  status          ClassesStatus?
  classesThought  ClassesThought[]
  ExamApplication ExamApplication[]
  questionAnswers Question_answer[]
  viewLogs        StudentClassViewLog[]
  savedByStudents StudentWishlist[]
  testimonials    Testimonial[]
  tuitionClasses  TuitionClass[]
}

model ClassesAbout {
  id             String   @id @default(uuid())
  birthDate      DateTime
  catchyHeadline String?
  tutorBio       String?
  profilePhoto   String?
  classesLogo    String?
  videoUrl       String?
  classId        String   @unique
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  class          Classes  @relation(fields: [classId], references: [id])
}

model AdminUser {
  id        String   @id @default(uuid())
  email     String   @unique
  password  String
  createdAt DateTime @default(now())
}

model ClassesExpereince {
  id             String   @id @default(uuid())
  classId        String
  title          String?
  certificateUrl String?
  from           String?
  to             String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  isExperience   Boolean  @default(true)
  status         Status   @default(PENDING)
  class          Classes  @relation(fields: [classId], references: [id])
}

model ClassesEducation {
  id          String   @id @default(uuid())
  classId     String
  university  String?
  degree      String?
  degreeType  String?
  passoutYear String?
  certificate String?
  createdAt   DateTime @default(now())
  isDegree    Boolean  @default(false)
  status      Status   @default(PENDING)
  class       Classes  @relation(fields: [classId], references: [id])
}

model ClassesCertificates {
  id             String   @id @default(uuid())
  classId        String
  title          String?
  certificateUrl String?
  createdAt      DateTime @default(now())
  isCertificate  Boolean  @default(true)
  status         Status   @default(PENDING)
  class          Classes  @relation(fields: [classId], references: [id])
}

model ClassesStatus {
  id        String              @id @default(uuid())
  classId   String              @unique
  status    ClassApprovalStatus
  createdAt DateTime            @default(now())
  class     Classes             @relation(fields: [classId], references: [id])
}

model UestCoins {
  id        String   @id @default(uuid())
  modelId   String
  modelType String
  coins     Float
  createdAt DateTime @default(now())

  @@unique([modelId, modelType])
}

model UestCoinTransaction {
  id        String          @id @default(uuid())
  modelId   String
  modelType String
  amount    Float
  type      TransactionType
  reason    String?
  createdAt DateTime        @default(now())
}

model TuitionClass {
  id           String   @id @default(uuid())
  classId      String
  education    String
  boardType    String?
  subject      String?
  medium       String?
  section      String?
  coachingType String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  details      String?
  class        Classes  @relation(fields: [classId], references: [id])
}

model ConstantCategory {
  id      String           @id @default(uuid())
  name    String           @unique
  details ConstantDetail[]
}

model ConstantDetail {
  id         String              @id @default(uuid())
  name       String
  categoryId String
  category   ConstantCategory    @relation(fields: [categoryId], references: [id])
  subDetails ConstantSubDetail[]

  @@unique([name, categoryId])
}

model ConstantSubDetail {
  id       String                   @id @default(uuid())
  name     String
  detailId String
  detail   ConstantDetail           @relation(fields: [detailId], references: [id])
  values   ConstantSubDetailValue[]

  @@unique([name, detailId])
}

model ConstantSubDetailValue {
  id          String            @id @default(uuid())
  name        String
  isActive    Boolean           @default(true)
  subDetailId String
  subDetail   ConstantSubDetail @relation(fields: [subDetailId], references: [id])

  @@unique([name, subDetailId])
}

model Exam {
  id                      Int               @id @default(autoincrement())
  exam_name               String
  start_date              DateTime
  duration                Int
  marks                   Decimal
  total_student_intake    Int
  level                   String
  createdAt               DateTime          @default(now())
  updatedAt               DateTime          @updatedAt
  total_questions         Int
  coins_required          Int?
  exam_type               ExamType?
  start_registration_date DateTime?
  ExamApplication         ExamApplication[]
  question_paper          Question_paper[]
  UwhizPriceRank          UwhizPriceRank[]
}

model UwhizPriceRank {
  id     String @id @default(uuid())
  examId Int
  rank   Int
  price  Int
  exam   Exam   @relation(fields: [examId], references: [id], onDelete: Cascade)
}

model Question_paper {
  id              Int               @id @default(autoincrement())
  question        String
  optionOne       String
  optionTwo       String
  optionThree     String
  optionFour      String
  correctAns      String
  examId          Int
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  question_answer Question_answer[]
  exam            Exam              @relation(fields: [examId], references: [id], onDelete: Cascade)
}

model Question_answer {
  id         Int            @id @default(autoincrement())
  userId     String
  questionId Int
  answer     String
  createdAt  DateTime       @default(now())
  isCorrect  Boolean        @default(false)
  question   Question_paper @relation(fields: [questionId], references: [id], onDelete: Cascade)
  user       Classes        @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([questionId])
}

model ExamApplication {
  id        String   @id @default(uuid())
  examId    Int
  classId   String
  createdAt DateTime @default(now())
  class     Classes  @relation(fields: [classId], references: [id])
  exam      Exam     @relation(fields: [examId], references: [id])

  @@unique([examId, classId])
}

model ClassesThought {
  id        String   @id @default(uuid())
  classId   String
  thoughts  String
  status    Status   @default(PENDING)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  class     Classes  @relation(fields: [classId], references: [id])
}

model Testimonial {
  id        String   @id @default(uuid())
  classId   String
  userId    String?
  message   String
  rating    Int
  status    Status   @default(PENDING)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  class     Classes  @relation(fields: [classId], references: [id])

  @@index([classId])
}

model Blog {
  id              String   @id @default(uuid())
  classId         String
  blogTitle       String
  blogImage       String
  blogDescription String
  status          Status   @default(PENDING)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  class           Classes  @relation(fields: [classId], references: [id])

  @@index([classId])
}

model Student {
  id             String                @id @default(uuid())
  firstName      String
  lastName       String
  email          String?               @unique
  contact        String?
  createdAt      DateTime              @default(now())
  updatedAt      DateTime              @updatedAt
  isVerified     Boolean               @default(false)
  googleId       String?               @unique
  profilePhoto   String?
  middleName     String?
  mothersName    String?
  studentReviews ClassesReviews[]
  classViewLogs  StudentClassViewLog[]
  profile        StudentProfile?
  wishlist       StudentWishlist[]
}

model StudentProfile {
  id           String   @id @default(uuid())
  studentId    String   @unique
  medium       String
  classroom    String
  birthday     DateTime
  school       String
  photo        String?
  documentUrl  String?
  status       Status   @default(PENDING)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  address      String?
  aadhaarNo    String?
  age          Int?
  birthPlace   String?
  bloodGroup   String?
  caste        String?
  contactNo2   String?
  gender       String?
  motherTongue String?
  religion     String?
  subCaste     String?
  uidNo        String?
  student      Student  @relation(fields: [studentId], references: [id])
}

model StudentWishlist {
  id           String   @id @default(uuid())
  studentId    String
  savedClassId String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  savedClass   Classes  @relation(fields: [savedClassId], references: [id])
  student      Student  @relation(fields: [studentId], references: [id])

  @@unique([studentId, savedClassId])
  @@index([studentId])
  @@index([savedClassId])
}

model ClassesReviews {
  id          String   @id @default(uuid())
  modelId     String
  modelType   String
  classId     String
  studentId   String?
  studentName String?
  userName    String?
  rating      Int
  message     String
  createdAt   DateTime @default(now())
  class       Classes  @relation(fields: [classId], references: [id])
  student     Student? @relation(fields: [studentId], references: [id])

  @@unique([modelId, modelType])
  @@index([classId])
  @@index([studentId])
  @@index([createdAt])
}

model ClassesCanApplyForQuestionBank {
  id          String  @id @default(uuid())
  classId     String
  hasEligible Boolean @default(false)
}

model ReferralLink {
  id        String     @id @default(uuid())
  userId    String
  userType  UserType
  code      String     @unique
  isActive  Boolean    @default(true)
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  referrals Referral[]

  @@index([userId, userType])
  @@index([code])
}

model Referral {
  id                String            @id @default(uuid())
  referralLinkId    String
  referredUserId    String
  referredUserType  UserType
  referredUserName  String
  referredUserEmail String
  createdAt         DateTime          @default(now())
  referralLink      ReferralLink      @relation(fields: [referralLinkId], references: [id])
  earnings          ReferralEarning[]

  @@index([referralLinkId])
  @@index([referredUserId, referredUserType])
  @@index([createdAt])
}

model ReferralEarning {
  id            String        @id @default(uuid())
  studentId     String
  examId        String?
  referralId    String
  earningType   EarningType
  amount        Float
  paymentStatus PaymentStatus @default(UNPAID)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  referral      Referral      @relation(fields: [referralId], references: [id])

  @@index([studentId])
  @@index([referralId])
  @@index([earningType])
  @@index([paymentStatus])
  @@index([createdAt])
}

model ChatMessage {
  id            String   @id @default(uuid())
  text          String
  senderId      String
  senderType    String
  recipientId   String?
  recipientType String?
  timestamp     DateTime @default(now())
  isRead        Boolean  @default(false)

  @@index([senderId])
  @@index([recipientId])
  @@index([recipientId, isRead])
}

model ExamMonitoringPhoto {
  id         String   @id @default(uuid())
  studentId  String
  examId     Int
  photoUrl   String
  capturedAt DateTime @default(now())
  createdAt  DateTime @default(now())

  @@index([studentId])
  @@index([examId])
  @@index([capturedAt])
  @@index([studentId, examId])
}

model ClassesAddress {
  id          String   @id @default(uuid())
  fullAddress String
  city        String?
  state       String?
  postcode    String?
  country     String?
  classId     String   @unique
  latitude    Float
  longitude   Float
  createdAt   DateTime @default(now())
  class       Classes  @relation(fields: [classId], references: [id])
}

model StudentClassViewLog {
  id        String   @id @default(uuid())
  studentId String
  classId   String
  viewedAt  DateTime @default(now())
  class     Classes  @relation(fields: [classId], references: [id])
  student   Student  @relation(fields: [studentId], references: [id])

  @@index([studentId])
  @@index([classId])
  @@index([viewedAt])
  @@index([studentId, classId])
}

model Notification {
  id        String           @id @default(uuid())
  userId    String
  userType  UserType
  type      NotificationType
  title     String
  message   String
  data      Json?
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  @@index([userId, userType])
  @@index([isRead])
  @@index([createdAt])
}

model OtpMessage {
  id           String   @id @default(uuid())
  contactNo    String
  otp          String
  createdAt    DateTime @default(now())
  expiredAt    DateTime
  requestCount Int      @default(0)
}

model batches {
  id                 BigInt               @id @default(autoincrement())
  class_uuid         String               @db.Uuid
  batch_name         String?              @db.VarChar(255)
  resource_id        BigInt?
  year_id            BigInt?
  created_at         DateTime?            @db.Timestamp(0)
  updated_at         DateTime?            @db.Timestamp(0)
  resources          resources?           @relation(fields: [resource_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "batches_resource_id_foreign")
  years              years?               @relation(fields: [year_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "batches_year_id_foreign")
  batches_classrooms batches_classrooms[]
  batches_days       batches_days[]
  batches_subjects   batches_subjects[]
  batches_timeslots  batches_timeslots[]
  @@ignore
}

model batches_classrooms {
  id           BigInt      @id @default(autoincrement())
  batch_id     BigInt?
  classroom_id BigInt?
  created_at   DateTime?   @db.Timestamp(0)
  updated_at   DateTime?   @db.Timestamp(0)
  batches      batches?    @relation(fields: [batch_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "batches_classrooms_batch_id_foreign")
  classrooms   classrooms? @relation(fields: [classroom_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "batches_classrooms_classroom_id_foreign")
  @@ignore
}

model batches_days {
  id         BigInt    @id @default(autoincrement())
  batch_id   BigInt?
  days       String?   @db.VarChar(255)
  created_at DateTime? @db.Timestamp(0)
  updated_at DateTime? @db.Timestamp(0)
  batches    batches?  @relation(fields: [batch_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "batches_days_batch_id_foreign")
  @@ignore
}

model batches_subjects {
  id         BigInt    @id @default(autoincrement())
  batch_id   BigInt?
  subject_id BigInt?
  created_at DateTime? @db.Timestamp(0)
  updated_at DateTime? @db.Timestamp(0)
  batches    batches?  @relation(fields: [batch_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "batches_subjects_batch_id_foreign")
  subjects   subjects? @relation(fields: [subject_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "batches_subjects_subject_id_foreign")
  @@ignore
}

model batches_timeslots {
  id          BigInt     @id @default(autoincrement())
  batch_id    BigInt?
  timeslot_id BigInt?
  created_at  DateTime?  @db.Timestamp(0)
  updated_at  DateTime?  @db.Timestamp(0)
  batches     batches?   @relation(fields: [batch_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "batches_timeslots_batch_id_foreign")
  timeslots   timeslots? @relation(fields: [timeslot_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "batches_timeslots_timeslot_id_foreign")
  @@ignore
}

model circulars {
  id         Int       @id @default(autoincrement())
  title      String    @db.VarChar(255)
  file       String    @db.VarChar(255)
  created_by String    @db.VarChar(255)
  class_uuid String    @db.Uuid
  created_at DateTime? @db.Timestamp(0)
  updated_at DateTime? @db.Timestamp(0)
  @@ignore
}

model classroom_category_fees_details {
  id                        BigInt                  @id @default(autoincrement())
  classroom_fees_details_id BigInt?
  category_id               Int
  amount                    Decimal                 @db.Decimal(8, 2)
  created_at                DateTime?               @db.Timestamp(0)
  updated_at                DateTime?               @db.Timestamp(0)
  classroom_fees_details    classroom_fees_details? @relation(fields: [classroom_fees_details_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "classroom_category_fees_details_classroom_fees_details_id_forei")
  @@ignore
}

model classroom_fees_details {
  id                              BigInt                            @id @default(autoincrement())
  classroom_id                    BigInt?
  year_id                         BigInt?
  lock                            Boolean                           @default(false)
  fee_type_id                     BigInt
  created_at                      DateTime?                         @db.Timestamp(0)
  updated_at                      DateTime?                         @db.Timestamp(0)
  classroom_category_fees_details classroom_category_fees_details[]
  fee_types                       fee_types                         @relation(fields: [fee_type_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "classroom_fees_details_fee_type_id_foreign")
  years                           years?                            @relation(fields: [year_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "classroom_fees_details_year_id_foreign")
  @@ignore
}

model classrooms {
  id                 BigInt               @id @default(autoincrement())
  class_uuid         String               @db.Uuid
  class_name         String               @db.VarChar(255)
  department_id      BigInt?
  year_id            BigInt?
  created_at         DateTime?            @db.Timestamp(0)
  updated_at         DateTime?            @db.Timestamp(0)
  batches_classrooms batches_classrooms[]
  department         department?          @relation(fields: [department_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "classrooms_department_id_foreign")
  years              years?               @relation(fields: [year_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "classrooms_year_id_foreign")
  enquiry            enquiry[]
  subjects           subjects[]
  @@ignore
}

model classworks {
  id             BigInt    @id @default(autoincrement())
  class_uuid     String?   @db.VarChar(255)
  classroom_id   String?   @db.VarChar(255)
  subject_id     String?   @db.VarChar(255)
  classwork_date DateTime? @db.Date
  title          String?   @db.VarChar(255)
  description    String?
  created_at     DateTime? @db.Timestamp(0)
  updated_at     DateTime? @db.Timestamp(0)
  @@ignore
}

model department {
  id                                     BigInt       @id @default(autoincrement())
  class_uuid                             String       @db.Uuid
  name                                   String       @db.VarChar(255)
  educational                            Int          @default(0)
  year_id                                BigInt?
  created_at                             DateTime?    @db.Timestamp(0)
  updated_at                             DateTime?    @db.Timestamp(0)
  deleted_at                             DateTime?    @db.Timestamp(0)
  classrooms                             classrooms[]
  years                                  years?       @relation(fields: [year_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "department_year_id_foreign")
  enquiry_enquiry_departmentTodepartment enquiry[]    @relation("enquiry_departmentTodepartment")
  @@ignore
}

model document_categories {
  id            BigInt      @id @default(autoincrement())
  category_name String      @db.VarChar(255)
  class_uuid    String      @db.Uuid
  is_optional   Boolean
  created_at    DateTime?   @db.Timestamp(0)
  updated_at    DateTime?   @db.Timestamp(0)
  documents     documents[]
  @@ignore
}

model documents {
  id                  Int                  @id @default(autoincrement())
  class_uuid          String               @db.Uuid
  document_name       String               @db.VarChar(255)
  file                String               @db.VarChar(255)
  category_id         BigInt?
  student_id          BigInt?
  other_category      String?              @db.VarChar(255)
  description         String?              @db.VarChar(255)
  created_by          String               @db.VarChar(255)
  created_at          DateTime?            @db.Timestamp(0)
  updated_at          DateTime?            @db.Timestamp(0)
  document_categories document_categories? @relation(fields: [category_id], references: [id], onUpdate: NoAction, map: "documents_category_id_foreign")
  @@ignore
}

model enquiry {
  id                                        BigInt              @id @default(autoincrement())
  class_uuid                                String              @db.Uuid
  student_first_name                        String?             @db.VarChar(255)
  student_middle_name                       String?             @db.VarChar(255)
  student_last_name                         String?             @db.VarChar(255)
  contact_number                            BigInt?
  email                                     String?             @db.VarChar(255)
  department                                BigInt?
  classroom                                 BigInt?
  comment                                   String?             @db.VarChar(1024)
  status                                    String?             @db.VarChar(255)
  created_at                                DateTime?           @db.Timestamp(0)
  updated_at                                DateTime?           @db.Timestamp(0)
  deleted_at                                DateTime?           @db.Timestamp(0)
  classrooms                                classrooms?         @relation(fields: [classroom], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "enquiry_classroom_foreign")
  department_enquiry_departmentTodepartment department?         @relation("enquiry_departmentTodepartment", fields: [department], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "enquiry_department_foreign")
  enquiry_fees                              enquiry_fees[]
  enquiry_followups                         enquiry_followups[]
  @@ignore
}

model enquiry_fee_setup {
  id            BigInt    @id @default(autoincrement())
  class_uuid    String    @db.Uuid
  shortlist_fee Decimal   @db.Decimal(15, 2)
  admission_fee Decimal   @db.Decimal(15, 2)
  created_at    DateTime? @db.Timestamp(0)
  updated_at    DateTime? @db.Timestamp(0)
  @@ignore
}

model enquiry_fees {
  id             BigInt    @id @default(autoincrement())
  enquiry_id     BigInt?
  payment_date   DateTime? @db.Date
  paid_amount    Decimal   @db.Decimal(15, 2)
  payment_mode   String    @db.VarChar(255)
  payment_status String?   @db.VarChar(255)
  cheque_no      String?   @db.VarChar(255)
  reference_no   String?   @db.VarChar(255)
  transaction_id String?   @db.VarChar(255)
  enquiry_status String?   @db.VarChar(255)
  created_at     DateTime? @db.Timestamp(0)
  updated_at     DateTime? @db.Timestamp(0)
  deleted_at     DateTime? @db.Timestamp(0)
  enquiry        enquiry?  @relation(fields: [enquiry_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "enquiry_fees_enquiry_id_foreign")
  @@ignore
}

model enquiry_followups {
  id         BigInt    @id @default(autoincrement())
  notes      String    @db.VarChar(1024)
  date       DateTime? @db.Date
  time       String?   @db.VarChar(255)
  status     String?   @db.VarChar(255)
  enquiry_id BigInt?
  created_by String?   @db.VarChar(255)
  updated_by String?   @db.VarChar(255)
  created_at DateTime? @db.Timestamp(0)
  updated_at DateTime? @db.Timestamp(0)
  deleted_at DateTime? @db.Timestamp(0)
  enquiry    enquiry?  @relation(fields: [enquiry_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "enquiry_followups_enquiry_id_foreign")
  @@ignore
}

model fee_types {
  id                     BigInt                   @id @default(autoincrement())
  name                   String                   @db.VarChar(255)
  description            String?                  @db.VarChar(255)
  total_installments     Int?
  created_at             DateTime?                @db.Timestamp(0)
  updated_at             DateTime?                @db.Timestamp(0)
  classroom_fees_details classroom_fees_details[]
  @@ignore
}

model fees_category {
  id            BigInt    @id @default(autoincrement())
  category_name String    @db.VarChar(255)
  year_id       BigInt?
  class_uuid    String    @db.Uuid
  created_at    DateTime? @db.Timestamp(0)
  updated_at    DateTime? @db.Timestamp(0)
  deleted_at    DateTime? @db.Timestamp(0)
  years         years?    @relation(fields: [year_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "fees_category_year_id_foreign")
  @@ignore
}

model holidays {
  id           BigInt    @id @default(autoincrement())
  class_uuid   String    @db.Uuid
  holiday_name String?   @db.VarChar(255)
  date         String?   @db.VarChar(255)
  year_id      BigInt?
  created_at   DateTime? @db.Timestamp(0)
  updated_at   DateTime? @db.Timestamp(0)
  years        years?    @relation(fields: [year_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "holidays_year_id_foreign")
  @@ignore
}

model homeworks {
  id            BigInt    @id @default(autoincrement())
  class_uuid    String?   @db.VarChar(255)
  classroom_id  String?   @db.VarChar(255)
  subject_id    String?   @db.VarChar(255)
  homework_date DateTime? @db.Date
  title         String?   @db.VarChar(255)
  description   String?
  created_at    DateTime? @db.Timestamp(0)
  updated_at    DateTime? @db.Timestamp(0)
  @@ignore
}

model jobs {
  id           BigInt @id @default(autoincrement())
  queue        String @db.VarChar(255)
  payload      String
  attempts     Int    @db.SmallInt
  reserved_at  Int?
  available_at Int
  created_at   Int

  @@index([queue], map: "jobs_queue_index")
  @@ignore
}

model migrations {
  id        Int    @id @default(autoincrement())
  migration String @db.VarChar(255)
  batch     Int
  @@ignore
}

model personal_access_tokens {
  id             BigInt    @id @default(autoincrement())
  tokenable_type String    @db.VarChar(255)
  tokenable_id   BigInt
  name           String    @db.VarChar(255)
  token          String    @unique(map: "personal_access_tokens_token_unique") @db.VarChar(64)
  abilities      String?
  last_used_at   DateTime? @db.Timestamp(0)
  expires_at     DateTime? @db.Timestamp(0)
  created_at     DateTime? @db.Timestamp(0)
  updated_at     DateTime? @db.Timestamp(0)

  @@index([tokenable_type, tokenable_id], map: "personal_access_tokens_tokenable_type_tokenable_id_index")
  @@ignore
}

model resources {
  id            BigInt    @id @default(autoincrement())
  class_uuid    String    @db.Uuid
  resource_name String    @db.VarChar(255)
  year_id       BigInt?
  created_at    DateTime? @db.Timestamp(0)
  updated_at    DateTime? @db.Timestamp(0)
  batches       batches[]
  years         years?    @relation(fields: [year_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "resources_year_id_foreign")
  @@ignore
}

model school_events {
  id         BigInt    @id @default(autoincrement())
  class_uuid String    @db.Uuid
  event_name String?   @db.VarChar(255)
  date       String?   @db.VarChar(255)
  year_id    BigInt?
  created_at DateTime? @db.Timestamp(0)
  updated_at DateTime? @db.Timestamp(0)
  years      years?    @relation(fields: [year_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "school_events_year_id_foreign")
  @@ignore
}

model student_academic_info {
  id                 BigInt               @id @default(autoincrement())
  student_id         String?
  class_uuid         String?              @db.VarChar(255)
  department         Int?
  classroom          Int?
  waypoint           Int?
  route              Int?
  year               Int?
  status             String               @default("ACTIVE") @db.VarChar(255)
  joining_date       DateTime?            @db.Date
  created_at         DateTime?            @db.Timestamp(0)
  updated_at         DateTime?            @db.Timestamp(0)
  deleted_at         DateTime?            @db.Timestamp(0)
  student_attendance student_attendance[]
  student_leaves     student_leaves[]
  @@ignore
}

model student_attendance {
  id                    BigInt                 @id @default(autoincrement())
  class_uuid            String                 @db.Uuid
  student_id            BigInt?
  date                  DateTime?              @db.Date
  present               Int?                   @default(0)
  discipline_issue      Json?                  @db.Json
  created_at            DateTime?              @db.Timestamp(0)
  updated_at            DateTime?              @db.Timestamp(0)
  student_academic_info student_academic_info? @relation(fields: [student_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "student_attendance_student_id_foreign")
  @@ignore
}

model student_leaves {
  id                    BigInt                 @id @default(autoincrement())
  leave_date            DateTime               @db.Date
  reason                String                 @db.VarChar(255)
  leave_status          String                 @db.VarChar(255)
  student_id            BigInt?
  created_at            DateTime?              @db.Timestamp(0)
  updated_at            DateTime?              @db.Timestamp(0)
  student_academic_info student_academic_info? @relation(fields: [student_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "student_leaves_student_id_foreign")
  @@ignore
}

model student_payment_details {
  id               BigInt    @id @default(autoincrement())
  student_id       BigInt?
  month_name       String?   @db.VarChar(255)
  paid_amount      Decimal   @db.Decimal(15, 2)
  payment_mode     String    @db.VarChar(255)
  payment_status   String?   @db.VarChar(255)
  cheque_no        String?   @db.VarChar(255)
  reference_no     String?   @db.VarChar(255)
  transaction_id   String?   @db.VarChar(255)
  payment_category Json?     @db.Json
  taken_by         BigInt?
  payment_date     DateTime? @db.Date
  created_at       DateTime? @db.Timestamp(0)
  updated_at       DateTime? @db.Timestamp(0)
  deleted_at       DateTime? @db.Timestamp(0)
  @@ignore
}

model subjects {
  id               BigInt             @id @default(autoincrement())
  class_uuid       String             @db.Uuid
  subject_name     String             @db.VarChar(255)
  classroom_id     BigInt?
  year_id          BigInt?
  created_at       DateTime?          @db.Timestamp(0)
  updated_at       DateTime?          @db.Timestamp(0)
  batches_subjects batches_subjects[]
  classrooms       classrooms?        @relation(fields: [classroom_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "subjects_classroom_id_foreign")
  years            years?             @relation(fields: [year_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "subjects_year_id_foreign")
  @@ignore
}

model timeslots {
  id                BigInt              @id @default(autoincrement())
  class_uuid        String              @db.Uuid
  start_time        String              @db.VarChar(255)
  end_time          String              @db.VarChar(255)
  is_break          String              @db.VarChar(255)
  break_name        String?             @db.VarChar(255)
  year_id           BigInt?
  created_at        DateTime?           @db.Timestamp(0)
  updated_at        DateTime?           @db.Timestamp(0)
  batches_timeslots batches_timeslots[]
  years             years?              @relation(fields: [year_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "timeslots_year_id_foreign")
  @@ignore
}

model year_with_user {
  id         BigInt    @id @default(autoincrement())
  year_id    BigInt?
  class_uuid String    @db.Uuid
  created_at DateTime? @db.Timestamp(0)
  updated_at DateTime? @db.Timestamp(0)
  years      years?    @relation(fields: [year_id], references: [id], onDelete: Restrict, onUpdate: NoAction, map: "year_with_user_year_id_foreign")
  @@ignore
}

model years {
  id                     BigInt                   @id @default(autoincrement())
  year_name              String                   @db.VarChar(255)
  start_date             DateTime?                @db.Date
  end_date               DateTime?                @db.Date
  status                 String?                  @db.VarChar(255)
  class_uuid             String                   @db.Uuid
  created_at             DateTime?                @db.Timestamp(0)
  updated_at             DateTime?                @db.Timestamp(0)
  deleted_at             DateTime?                @db.Timestamp(0)
  batches                batches[]
  classroom_fees_details classroom_fees_details[]
  classrooms             classrooms[]
  department             department[]
  fees_category          fees_category[]
  holidays               holidays[]
  resources              resources[]
  school_events          school_events[]
  subjects               subjects[]
  timeslots              timeslots[]
  year_with_user         year_with_user[]
  @@ignore
}

model StoreItem {
  id             String   @id @default(uuid())
  name           String
  description    String
  coinPrice      Int
  totalStock     Int @default(0)
  availableStock Int @default(0)
  category       String
  image          String?
  status         StoreItemStatus @default(ACTIVE)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  orders         StoreOrder[]
  cartItems      StoreCart[]
  @@index([category])
  @@index([status])
}

model StoreOrder {
  id            String   @id @default(uuid())
  modelId       String
  modelType     ModelType
  itemId        String
  itemName      String
  itemPrice     Int
  quantity      Int
  totalCoins    Int
  status        StoreOrderStatus @default(PENDING)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  item          StoreItem? @relation(fields: [itemId], references: [id])
  @@index([modelId])
  @@index([modelType])
  @@index([itemId])
  @@index([status])
  @@index([createdAt])
}

model StoreCart {
  id        String   @id @default(uuid())
  userId    String
  userType  ModelType
  itemId    String
  quantity  Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  item      StoreItem @relation(fields: [itemId], references: [id], onDelete: Cascade)

  @@unique([userId, userType, itemId])
  @@index([userId])
  @@index([userType])
  @@index([itemId])
}

enum StoreItemStatus {
  ACTIVE
  INACTIVE
}

enum StoreOrderStatus {
  PENDING
  COMPLETED
  CANCELLED
}

enum ClassApprovalStatus {
  PENDING
  APPROVED
  REJECTED
  IN_PROCESS
}

enum ModelType {
  CLASS
  STUDENT
  SCHOOL
}

enum TransactionType {
  CREDIT
  DEBIT
}

enum UserType {
  ADMIN
  CLASS
  STUDENT
}

enum ExamType {
  CLASSES
  STUDENTS
}

enum Status {
  PENDING
  APPROVED
  REJECTED
}

enum EarningType {
  REGISTRATION
  UWHIZ_APPLICATION
}

enum PaymentStatus {
  PAID
  UNPAID
}

enum NotificationType {
  STUDENT_ACCOUNT_CREATED
  STUDENT_COIN_PURCHASE
  STUDENT_UWHIZ_PARTICIPATION
  STUDENT_PROFILE_APPROVED
  STUDENT_PROFILE_REJECTED
  STUDENT_CHAT_MESSAGE
  STUDENT_STORE_PURCHASE
  STUDENT_STORE_ORDER_APPROVED
  STUDENT_STORE_ORDER_REJECTED
  CLASS_ACCOUNT_CREATED
  CLASS_COIN_PURCHASE
  CLASS_PROFILE_APPROVED
  CLASS_PROFILE_REJECTED
  CLASS_CHAT_MESSAGE
  CLASS_CONTENT_APPROVED
  CLASS_CONTENT_REJECTED
  CLASS_EDUCATION_ADDED
  CLASS_EXPERIENCE_ADDED
  CLASS_CERTIFICATE_ADDED
  CLASS_STORE_PURCHASE
  CLASS_STORE_ORDER_APPROVED
  CLASS_STORE_ORDER_REJECTED
  ADMIN_NEW_STUDENT_REGISTRATION
  ADMIN_NEW_CLASS_REGISTRATION
  ADMIN_PROFILE_REVIEW_REQUIRED
  ADMIN_CONTENT_REVIEW_REQUIRED
  ADMIN_NEW_STORE_ORDER
  EXAM_APPLICATION_SUCCESS
  NEW_EXAM_APPLICATION
}

model UserActivityLog {
  id          String           @id @default(uuid())
  userId      String
  userType    UserType
  activityType String
  createdAt   DateTime         @default(now())

  @@index([userId, userType])
  @@index([activityType])
  @@index([createdAt])
  @@index([userId, userType, activityType])
}
