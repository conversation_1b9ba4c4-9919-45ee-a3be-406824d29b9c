/******/ (() => { // webpackBootstrap
/*!******************************************************!*\
  !*** ./modules/Batches/resources/views/js/create.js ***!
  \******************************************************/
$("#createbatches_form").submit(function () {
  event.preventDefault();
  var form = $(this);
  if ($(this).valid()) {
    ajaxHandler(form, createbatchesRoute.store, 'post', '#createbatches_form', '#savebatches', '#newBatchesEntry', '#batches_table');
    return false;
  }
});
/******/ })()
;