{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "barryvdh/laravel-dompdf": "^2.2", "doctrine/dbal": "^3.10", "firebase/php-jwt": "^6.11", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^9.19", "laravel/sanctum": "^3.0", "laravel/tinker": "^2.7", "laravel/ui": "^4.6", "laravelcollective/html": "^6.4", "maatwebsite/excel": "^3.1.48", "proengsoft/laravel-jsvalidation": "^4.9", "silviolleite/laravelpwa": "^2.0", "simplesoftwareio/simple-qrcode": "^4.2", "spatie/laravel-permission": "^6.16", "yajra/laravel-datatables-oracle": "^10.11"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Module\\": "modules/", "Department\\": "modules/Department/app/", "DepartmentSeeders\\": "modules/Department/database/seeders/", "Classroom\\": "modules/Classroom/app/", "ClassroomSeeders\\": "modules/Classroom/database/seeders/", "Subject\\": "modules/Subject/app/", "SubjectSeeders\\": "modules/Subject/database/seeders/", "Resources\\": "modules/Resources/app/", "ResourcesSeeders\\": "modules/Resources/database/seeders/", "Timeslots\\": "modules/Timeslots/app/", "TimeslotsSeeders\\": "modules/Timeslots/database/seeders/", "Years\\": "modules/Years/app/", "YearsSeeders\\": "modules/Years/database/seeders/", "Circulars\\": "modules/Circulars/app/", "CircularsSeeders\\": "modules/Circulars/database/seeders/", "Document\\": "modules/Document/app/", "DocumentsSeeders\\": "modules/Document/database/seeders/", "DocumentCategory\\": "modules/DocumentCategory/app/", "DocumentCategorySeeders\\": "modules/DocumentCategory/database/seeders/", "Holiday\\": "modules/Holiday/app/", "HolidaySeeders\\": "modules/Holiday/database/seeders/", "Event\\": "modules/Event/app/", "EventSeeders\\": "modules/Event/database/seeders/", "ProfileViews\\": "modules/ProfileViews/app/", "ProfileViewsSeeders\\": "modules/ProfileViews/database/seeders/", "Enquiry\\": "modules/Enquiry/app/", "EnquirySeeders\\": "modules/Enquiry/database/seeders/", "StudentAttendance\\": "modules/StudentAttendance/app/", "StudentAttendanceSeeders\\": "modules/StudentAttendance/database/seeders/", "Admission\\": "modules/Admission/app/", "AdmissionSeeders\\": "modules/Admission/database/seeders/", "AnnualCalendar\\": "modules/AnnualCalendar/app/", "AnnualCalendarSeeders\\": "modules/AnnualCalendar/database/seeders/", "Fees\\": "modules/Fees/app/", "FeesSeeders\\": "modules/Fees/database/seeders/", "Passbook\\": "modules/Passbook/app/", "PassbookSeeders\\": "modules/Passbook/database/seeders/", "ClassWorkSeeders\\": "modules/ClassWork/database/seeders/", "ClassWork\\": "modules/ClassWork/app/", "HomeWorkSeeders\\": "modules/HomeWork/database/seeders/", "HomeWork\\": "modules/HomeWork/app/", "ThoughtsSeeders\\": "modules/Thoughts/database/seeders/", "Thoughts\\": "modules/Thoughts/app/", "TestimonialSeeders\\": "modules/Testimonial/database/seeders/", "Testimonial\\": "modules/Testimonial/app/", "BlogsSeeders\\": "modules/Blogs/database/seeders/", "Blogs\\": "modules/Blogs/app/", "StudentLeave\\": "modules/StudentLeave/app/", "StudentLeaveSeeders\\": "modules/StudentLeave/database/seeders/", "Batches\\": "modules/Batches/app/", "BatchesLeaveSeeders\\": "modules/Batches/database/seeders/"}, "files": ["app/Http/Helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true}