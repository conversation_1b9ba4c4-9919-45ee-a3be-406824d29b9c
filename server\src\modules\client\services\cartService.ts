import prisma from '@/config/prismaClient';
import { ModelType } from '@prisma/client';

interface CartItem {
  id: string;
  userId: string;
  userType: ModelType;
  itemId: string;
  quantity: number;
  createdAt: Date;
  updatedAt: Date;
  item: {
    id: string;
    name: string;
    coinPrice: number;
    image: string | null;
    availableStock: number;
  };
}

export const addToCart = async (userId: string, userType: ModelType, itemId: string, quantity: number = 1) => {
  try {
    const storeItem = await prisma.storeItem.findUnique({
      where: { id: itemId },
      select: { id: true, availableStock: true, status: true }
    });

    if (!storeItem) {
      return { success: false, error: 'Item not found' };
    }

    if (storeItem.status !== 'ACTIVE') {
      return { success: false, error: 'Item is not available' };
    }

    if (storeItem.availableStock < quantity) {
      return { success: false, error: 'Insufficient stock' };
    }

    const existingCartItem = await prisma.storeCart.findUnique({
      where: {
        userId_userType_itemId: {
          userId,
          userType,
          itemId
        }
      }
    });

    if (existingCartItem) {
      const newQuantity = existingCartItem.quantity + quantity;
      
      if (newQuantity > storeItem.availableStock) {
        return { success: false, error: 'Cannot add more items than available stock' };
      }

      const updatedCartItem = await prisma.storeCart.update({
        where: { id: existingCartItem.id },
        data: { quantity: newQuantity },
        include: {
          item: {
            select: {
              id: true,
              name: true,
              coinPrice: true,
              image: true,
              availableStock: true
            }
          }
        }
      });

      return { success: true, data: updatedCartItem };
    } else {
      const cartItem = await prisma.storeCart.create({
        data: {
          userId,
          userType,
          itemId,
          quantity
        },
        include: {
          item: {
            select: {
              id: true,
              name: true,
              coinPrice: true,
              image: true,
              availableStock: true
            }
          }
        }
      });

      return { success: true, data: cartItem };
    }
  } catch (error) {
    console.error('Error adding to cart:', error);
    return { success: false, error: 'Failed to add item to cart' };
  }
};

export const getCartItems = async (userId: string, userType: ModelType): Promise<{ success: boolean; data?: CartItem[]; error?: string }> => {
  try {
    const cartItems = await prisma.storeCart.findMany({
      where: {
        userId,
        userType
      },
      include: {
        item: {
          select: {
            id: true,
            name: true,
            coinPrice: true,
            image: true,
            availableStock: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return { success: true, data: cartItems };
  } catch (error) {
    console.error('Error fetching cart items:', error);
    return { success: false, error: 'Failed to fetch cart items' };
  }
};

export const updateCartItemQuantity = async (userId: string, userType: ModelType, itemId: string, quantity: number) => {
  try {
    if (quantity <= 0) {
      return removeFromCart(userId, userType, itemId);
    }

    const storeItem = await prisma.storeItem.findUnique({
      where: { id: itemId },
      select: { availableStock: true }
    });

    if (!storeItem) {
      return { success: false, error: 'Item not found' };
    }

    if (quantity > storeItem.availableStock) {
      return { success: false, error: 'Quantity exceeds available stock' };
    }

    const updatedCartItem = await prisma.storeCart.update({
      where: {
        userId_userType_itemId: {
          userId,
          userType,
          itemId
        }
      },
      data: { quantity },
      include: {
        item: {
          select: {
            id: true,
            name: true,
            coinPrice: true,
            image: true,
            availableStock: true
          }
        }
      }
    });

    return { success: true, data: updatedCartItem };
  } catch (error) {
    console.error('Error updating cart item quantity:', error);
    return { success: false, error: 'Failed to update cart item' };
  }
};

export const removeFromCart = async (userId: string, userType: ModelType, itemId: string) => {
  try {
    await prisma.storeCart.delete({
      where: {
        userId_userType_itemId: {
          userId,
          userType,
          itemId
        }
      }
    });

    return { success: true };
  } catch (error) {
    console.error('Error removing from cart:', error);
    return { success: false, error: 'Failed to remove item from cart' };
  }
};

export const clearCart = async (userId: string, userType: ModelType) => {
  try {
    await prisma.storeCart.deleteMany({
      where: {
        userId,
        userType
      }
    });

    return { success: true };
  } catch (error) {
    console.error('Error clearing cart:', error);
    return { success: false, error: 'Failed to clear cart' };
  }
};

export const getCartTotal = async (userId: string, userType: ModelType) => {
  try {
    const cartItems = await prisma.storeCart.findMany({
      where: {
        userId,
        userType
      },
      include: {
        item: {
          select: {
            coinPrice: true
          }
        }
      }
    });

    const totalCoins = cartItems.reduce((total: number, item: any) => {
      return total + (item.item.coinPrice * item.quantity);
    }, 0);

    const totalItems = cartItems.reduce((total: number, item: any) => {
      return total + item.quantity;
    }, 0);

    return { 
      success: true, 
      data: { 
        totalCoins, 
        totalItems,
        itemCount: cartItems.length 
      } 
    };
  } catch (error) {
    console.error('Error calculating cart total:', error);
    return { success: false, error: 'Failed to calculate cart total' };
  }
};
